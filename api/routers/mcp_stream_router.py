"""
MCP Streamable HTTP router for FastAPI using transport-based implementation.

This module provides Streamable HTTP endpoints for the Model Context Protocol (MCP).
It supports both general tool listing and tag-filtered tool listing with concurrent-safe design.

Features:
- Concurrent-safe design with per-connection handlers
- Automatic pagination for large tool sets
- Tag-based tool filtering
- Comprehensive error handling and logging
- Streamable HTTP transport for better performance
"""

import json
import logging
from typing import Dict, Any, List, Optional

import mcp.types as types
from fastapi import APIRouter, Request, Depends
from mcp.server.lowlevel import Server
from mcp.server.streamable_http import StreamableHTTPServerTransport
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.services.tag_service import TagService
from api.services.tool_service import ToolService

MCP_SERVER_NAME = "Easy MCP Streamable HTTP Server"

# Create logger
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter(tags=["mcp-stream"])

# Note: For streamable HTTP, we create transport instances per connection
# to ensure proper session management and concurrent safety


class MCPStreamServerHandler:
    """
    MCP Streamable HTTP Server handler with database session and tag filter.

    This class provides a concurrent-safe way to handle MCP requests with
    per-connection database sessions and optional tag filtering for streamable HTTP.
    """

    def __init__(self, db_session: AsyncSession, tag_filter: Optional[str] = None):
        """
        Initialize handler with database session and optional tag filter.

        Args:
            db_session: Database session for this connection
            tag_filter: Optional tag name to filter tools by
        """
        self.db_session = db_session
        self.tag_filter = tag_filter
        self._tool_service = ToolService(db_session)
        self._tag_service = TagService(db_session) if tag_filter else None

    async def list_tools(self) -> List[types.Tool]:
        """
        Get all enabled tools from database and convert to MCP Tool format.

        Returns:
            List of MCP Tool objects for enabled tools
        """
        try:
            # Get tag IDs if tag_filter is provided
            tag_ids = await self._get_tag_ids() if self.tag_filter else None
            if self.tag_filter and tag_ids is None:
                return []  # Tag not found

            # Get all tools using pagination
            all_tools = await self._get_all_tools_paginated(tag_ids)

            # Convert enabled tools to MCP format
            mcp_tools = self._convert_tools_to_mcp_format(all_tools)

            # Log results
            self._log_tool_loading_results(mcp_tools, all_tools)

            return mcp_tools

        except Exception as e:
            logger.error(f"Error getting enabled tools: {e}")
            return []

    async def _get_tag_ids(self) -> Optional[List[int]]:
        """
        Get tag IDs for the current tag filter.

        Returns:
            List of tag IDs or None if tag not found
        """
        if not self._tag_service:
            return None

        tag = await self._tag_service.get_tag_by_name(self.tag_filter)
        if tag:
            logger.info(f"Filtering tools by tag: {self.tag_filter} (ID: {tag.id})")
            return [tag.id]
        else:
            logger.warning(f"Tag not found: {self.tag_filter}")
            return None

    async def _get_all_tools_paginated(self, tag_ids: Optional[List[int]]) -> List:
        """
        Get all tools using pagination to handle large datasets.

        Args:
            tag_ids: Optional list of tag IDs to filter by

        Returns:
            List of all tools from database
        """
        all_tools = []
        page = 1

        while True:
            tools, total = await self._tool_service.query_tools(
                page=page,
                size=100,
                tag_ids=tag_ids
            )

            if not tools:
                break

            all_tools.extend(tools)

            # Check if we've got all tools
            if len(all_tools) >= total:
                break

            page += 1

        return all_tools

    def _convert_tools_to_mcp_format(self, tools: List) -> List[types.Tool]:
        """
        Convert database tools to MCP Tool format, filtering enabled tools only.

        Args:
            tools: List of database tool objects

        Returns:
            List of MCP Tool objects
        """
        mcp_tools = []

        for tool in tools:
            if not tool.is_enabled:
                continue

            try:
                # Parse parameters JSON Schema
                parameters = self._parse_tool_parameters(tool.parameters)

                mcp_tool = types.Tool(
                    name=tool.name,
                    description=tool.description or "",
                    inputSchema=parameters,
                )
                mcp_tools.append(mcp_tool)

            except Exception as e:
                logger.error(f"Error converting tool {tool.name}: {e}")
                continue

        return mcp_tools

    def _parse_tool_parameters(self, parameters_str: Optional[str]) -> Dict[str, Any]:
        """
        Parse tool parameters JSON string.

        Args:
            parameters_str: JSON string of parameters

        Returns:
            Parsed parameters dictionary
        """
        if not parameters_str:
            return {}

        try:
            return json.loads(parameters_str)
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON in tool parameters: {e}")
            return {}

    def _log_tool_loading_results(self, mcp_tools: List[types.Tool], all_tools: List):
        """
        Log the results of tool loading.

        Args:
            mcp_tools: List of converted MCP tools
            all_tools: List of all tools from database
        """
        if self.tag_filter:
            logger.info(
                f"Loaded {len(mcp_tools)} enabled tools for tag '{self.tag_filter}' "
                f"(total: {len(all_tools)})"
            )
        else:
            logger.info(f"Loaded {len(mcp_tools)} enabled tools (total: {len(all_tools)})")

    async def execute_tool(self, name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
        """
        Execute a tool by name with given arguments.

        Args:
            name: Tool name to execute
            arguments: Tool execution arguments

        Returns:
            List of TextContent with execution results
        """
        try:
            # Get tool by name
            tool = await self._tool_service.get_tool_by_name(name)
            if not tool:
                return [self._create_error_response(f"Tool '{name}' not found")]

            if not tool.is_enabled:
                return [self._create_error_response(f"Tool '{name}' is disabled")]

            # Execute tool
            result, logs = await self._tool_service.execute_tool(
                tool.id, arguments, call_type="mcp"
            )

            # Format and return result
            result_text = self._format_execution_result(result)
            return [types.TextContent(type="text", text=result_text)]

        except Exception as e:
            logger.error(f"Error executing tool {name}: {e}")
            return [self._create_error_response(f"Error executing tool: {str(e)}")]

    def _create_error_response(self, message: str) -> types.TextContent:
        """
        Create a standardized error response.

        Args:
            message: Error message

        Returns:
            TextContent with error message
        """
        return types.TextContent(type="text", text=message)

    def _format_execution_result(self, result: Any) -> str:
        """
        Format tool execution result as text.

        Args:
            result: Tool execution result

        Returns:
            Formatted result string
        """
        if result is None:
            return "No result returned"

        if isinstance(result, (dict, list)):
            try:
                return json.dumps(result, ensure_ascii=False, indent=2)
            except (TypeError, ValueError) as e:
                logger.warning(f"Failed to serialize result to JSON: {e}")
                return str(result)

        return str(result)


def create_mcp_stream_server(handler: MCPStreamServerHandler) -> Server:
    """
    Create a new MCP server instance with the given handler for streamable HTTP.

    This function creates a per-connection MCP server instance to ensure
    concurrent safety and proper resource isolation.

    Args:
        handler: MCPStreamServerHandler instance for this connection

    Returns:
        Configured MCP server instance
    """
    server = Server(MCP_SERVER_NAME)

    @server.list_tools()
    async def list_tools() -> List[types.Tool]:
        """List available tools for this connection."""
        return await handler.list_tools()

    @server.call_tool()
    async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Handle tool execution for this connection."""
        return await handler.execute_tool(name, arguments)

    return server


# FastAPI endpoints
@router.post("/stream")
async def handle_stream_endpoint(request: Request, db: AsyncSession = Depends(get_db)):
    """
    Handle Streamable HTTP connection for MCP without tag filtering.

    This endpoint provides access to all enabled tools in the system.

    Args:
        request: FastAPI request object
        db: Database session dependency
    """
    # Create handler without tag filter
    handler = MCPStreamServerHandler(db)

    # Create MCP server instance for this connection
    mcp_server = create_mcp_stream_server(handler)

    # Create transport and establish connection
    transport = StreamableHTTPServerTransport(mcp_session_id=None)

    # Connect and handle request
    async with transport.connect() as streams:
        # Start the MCP server in the background
        import asyncio

        # Create an event to signal when the server is initialized
        server_initialized = asyncio.Event()

        async def run_server_with_init_signal():
            """Run the server and signal when initialization is complete."""
            try:
                # Create initialization options
                init_options = mcp_server.create_initialization_options()

                # Signal that we're about to initialize
                server_initialized.set()

                # Run the server
                await mcp_server.run(
                    streams[0],  # read stream
                    streams[1],  # write stream
                    init_options,
                )
            except Exception as e:
                logger.error(f"Error running MCP server: {e}")
                raise

        server_task = asyncio.create_task(run_server_with_init_signal())

        # Wait for server initialization
        await server_initialized.wait()

        # Give additional time for complete initialization
        await asyncio.sleep(0.2)

        try:
            # Handle the HTTP request
            await transport.handle_request(
                request.scope,
                request.receive,
                request._send
            )
        finally:
            # Clean up the server task
            if not server_task.done():
                server_task.cancel()
                try:
                    await server_task
                except asyncio.CancelledError:
                    pass


@router.post("/stream-{tag}")
async def handle_stream_endpoint_with_tag(
    tag: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Streamable HTTP connection for MCP with tag filtering.

    This endpoint provides access to tools filtered by a specific tag.
    If the tag doesn't exist, an empty tool list will be returned.

    Args:
        tag: Tag name to filter tools by
        request: FastAPI request object
        db: Database session dependency
    """
    # Create handler with tag filter
    handler = MCPStreamServerHandler(db, tag_filter=tag)

    # Create MCP server instance for this connection
    mcp_server = create_mcp_stream_server(handler)

    # Create transport and establish connection
    transport = StreamableHTTPServerTransport(mcp_session_id=None)

    # Connect and handle request
    async with transport.connect() as streams:
        # Start the MCP server in the background
        import asyncio
        server_task = asyncio.create_task(
            mcp_server.run(
                streams[0],  # read stream
                streams[1],  # write stream
                mcp_server.create_initialization_options(),
            )
        )

        try:
            # Handle the HTTP request
            await transport.handle_request(
                request.scope,
                request.receive,
                request._send
            )
        finally:
            # Clean up the server task
            if not server_task.done():
                server_task.cancel()
                try:
                    await server_task
                except asyncio.CancelledError:
                    pass
