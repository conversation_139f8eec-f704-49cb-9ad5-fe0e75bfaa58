import{_ as Q,u as W,r as _,a as X,o as Z,b as d,c,d as h,w as e,e as t,h as ee,g as s,f as w,M as te,t as y,k as f,F as D,N as ae,l as S}from"./index-01e50f26.js";import{c as oe,A as le,f as ne}from"./AppLayout-94293e8c.js";import{C as se}from"./ClearOutlined-b38cf02d.js";const ue={class:"action-bar"},re=["onClick"],ce={key:1},ie={key:3},de={key:4},_e={key:0},pe={__name:"AuditListView",setup(fe){const g=W(),C=_(!1),A=_([]),z=_(0),v=_(1),k=_(10),n=X({username:"",action:void 0,resource_type:void 0,resource_name:""}),p=_([]),b=_(!1),x=_(null),N=[{title:"用户名",dataIndex:"username",key:"username",width:120},{title:"操作类型",key:"action",width:100},{title:"资源类型",key:"resource_type",width:100},{title:"资源ID",key:"resource_id",width:80},{title:"资源名称",key:"resource_name",width:150,ellipsis:!0,tooltip:!0},{title:"IP地址",dataIndex:"ip_address",key:"ip_address",width:120},{title:"操作时间",key:"created_at",width:180},{title:"详情",key:"details",width:100}];Z(()=>{m()});const m=async()=>{C.value=!0;try{const a={page:v.value,size:k.value};n.username&&(a.username=n.username),n.action&&(a.action=n.action),n.resource_type&&(a.resource_type=n.resource_type),n.resource_name&&(a.resource_name=n.resource_name),p.value&&p.value.length===2&&(a.start_time=p.value[0].valueOf(),a.end_time=p.value[1].valueOf()),await oe({method:"get",url:"/api/v1/audit",params:a,onSuccess:(o,i)=>{A.value=o,z.value=i.total},errorMessage:"获取审计日志失败"})}finally{C.value=!1}},V=()=>{v.value=1,m()},U=()=>{n.username="",n.action=void 0,n.resource_type=void 0,n.resource_name="",p.value=[],v.value=1,m()},I=()=>{V()},T=(a,o)=>{v.value=a,k.value=o,m()},B=(a,o)=>{v.value=1,k.value=o,m()},L=a=>({create:"创建",update:"更新",delete:"删除",deploy:"发布",rollback:"回滚",import:"导入"})[a]||a,P=a=>({create:"green",update:"blue",delete:"red",deploy:"purple",rollback:"orange"})[a]||"default",Y=a=>({user:"用户",tool:"工具",func:"函数",config:"配置",tag:"标签"})[a]||a,E=a=>({user:"cyan",tool:"blue",func:"green",config:"purple"})[a]||"default",F=a=>{const{resource_type:o,resource_id:i}=a;if(i)switch(o){case"user":g.push(`/user/${i}/edit`);break;case"tool":g.push(`/tool/${i}`);break;case"func":g.push(`/func/${i}`);break;case"config":g.push(`/config/${i}`);break}},H=a=>{x.value=a,b.value=!0};return(a,o)=>{const i=d("a-input"),u=d("a-select-option"),O=d("a-select"),J=d("a-range-picker"),R=d("a-space"),M=d("a-button"),$=d("a-tag"),j=d("a-table"),q=d("a-modal"),G=d("a-card");return c(),h(le,{"current-page-key":"audit"},{default:e(()=>[t(G,{title:"审计日志"},{default:e(()=>[ee("div",ue,[t(R,null,{default:e(()=>[t(i,{value:n.username,"onUpdate:value":o[0]||(o[0]=l=>n.username=l),placeholder:"用户名",style:{width:"150px"},"allow-clear":""},null,8,["value"]),t(O,{value:n.action,"onUpdate:value":o[1]||(o[1]=l=>n.action=l),placeholder:"操作类型",style:{width:"120px"},"allow-clear":""},{default:e(()=>[t(u,{value:"create"},{default:e(()=>[s("创建")]),_:1}),t(u,{value:"update"},{default:e(()=>[s("更新")]),_:1}),t(u,{value:"delete"},{default:e(()=>[s("删除")]),_:1}),t(u,{value:"deploy"},{default:e(()=>[s("发布")]),_:1}),t(u,{value:"rollback"},{default:e(()=>[s("回滚")]),_:1}),t(u,{value:"import"},{default:e(()=>[s("导入")]),_:1})]),_:1},8,["value"]),t(O,{value:n.resource_type,"onUpdate:value":o[2]||(o[2]=l=>n.resource_type=l),placeholder:"资源类型",style:{width:"120px"},"allow-clear":""},{default:e(()=>[t(u,{value:"user"},{default:e(()=>[s("用户")]),_:1}),t(u,{value:"tool"},{default:e(()=>[s("工具")]),_:1}),t(u,{value:"func"},{default:e(()=>[s("函数")]),_:1}),t(u,{value:"config"},{default:e(()=>[s("配置")]),_:1}),t(u,{value:"tag"},{default:e(()=>[s("标签")]),_:1})]),_:1},8,["value"]),t(i,{value:n.resource_name,"onUpdate:value":o[3]||(o[3]=l=>n.resource_name=l),placeholder:"资源名称",style:{width:"150px"},"allow-clear":""},null,8,["value"]),t(J,{value:p.value,"onUpdate:value":o[4]||(o[4]=l=>p.value=l),"show-time":"",format:"YYYY-MM-DD HH:mm:ss",onChange:I,placeholder:["开始时间","结束时间"]},null,8,["value"])]),_:1}),t(R,null,{default:e(()=>[t(M,{type:"primary",onClick:V},{icon:e(()=>[t(w(te))]),default:e(()=>[s(" 查询 ")]),_:1}),t(M,{onClick:U},{icon:e(()=>[t(w(se))]),default:e(()=>[s(" 重置 ")]),_:1})]),_:1})]),t(j,{columns:N,"data-source":A.value,loading:C.value,pagination:{current:v.value,pageSize:k.value,total:z.value,onChange:T,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:B,showTotal:l=>`共 ${l} 条记录`},"row-key":l=>l.id},{bodyCell:e(({column:l,record:r})=>[l.key==="action"?(c(),h($,{key:0,color:P(r.action)},{default:e(()=>[s(y(L(r.action)),1)]),_:2},1032,["color"])):l.key==="resource_type"?(c(),h($,{key:1,color:E(r.resource_type)},{default:e(()=>[s(y(Y(r.resource_type)),1)]),_:2},1032,["color"])):l.key==="resource_id"?(c(),f(D,{key:2},[r.resource_id?(c(),f("a",{key:0,onClick:K=>F(r)},y(r.resource_id),9,re)):(c(),f("span",ce,"-"))],64)):l.key==="resource_name"?(c(),f("span",ie,y(r.resource_name||"-"),1)):l.key==="created_at"?(c(),f("span",de,y(w(ne)(r.created_at)),1)):l.key==="details"?(c(),f(D,{key:5},[r.details?(c(),h(M,{key:0,type:"link",size:"small",onClick:K=>H(r)},{icon:e(()=>[t(w(ae))]),default:e(()=>[s(" 查看 ")]),_:2},1032,["onClick"])):S("",!0)],64)):S("",!0)]),_:1},8,["data-source","loading","pagination","row-key"]),t(q,{open:b.value,"onUpdate:open":o[5]||(o[5]=l=>b.value=l),title:"操作详情",width:"800px",footer:null,"ok-text":"确定","cancel-text":"取消"},{default:e(()=>[x.value?(c(),f("pre",_e,y(JSON.stringify(x.value.details,null,2)),1)):S("",!0)]),_:1},8,["open"])]),_:1})]),_:1})}}},ge=Q(pe,[["__scopeId","data-v-8e36bfe7"]]);export{ge as default};
