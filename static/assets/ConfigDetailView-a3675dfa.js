import{_ as ae,u as ne,y as ce,z as V,r as h,o as le,b as _,c as a,d as g,w as n,e as c,f as v,g as m,E as ie,h as o,t as u,k as r,F as T,v as P,l as b,q as re,p as ue,m as _e}from"./index-01e50f26.js";import{c as D,A as de,S as ve,f as q,T as fe}from"./AppLayout-94293e8c.js";import{_ as pe}from"./JsonSchemaForm-eb887394.js";import{R as me}from"./RollbackOutlined-f857b260.js";import{Q as he}from"./QuestionCircleOutlined-2804346b.js";const f=k=>(ue("data-v-1fd9680a"),k=k(),_e(),k),ge={class:"info-container"},ye={class:"info-row"},be=f(()=>o("div",{class:"info-label"},"配置名称:",-1)),ke={class:"info-value"},we={class:"info-row"},Ce=f(()=>o("div",{class:"info-label"},"配置描述:",-1)),Se={class:"info-value"},Oe={class:"info-row"},Ve=f(()=>o("div",{class:"info-label"},"创建时间:",-1)),$e={class:"info-value"},xe={class:"info-row"},Ne=f(()=>o("div",{class:"info-label"},"更新时间:",-1)),Ee={class:"info-value"},Te={class:"info-row"},De=f(()=>o("div",{class:"info-label"},"创建人:",-1)),Me={class:"info-value"},Re={class:"info-row"},je=f(()=>o("div",{class:"info-label"},"更新人:",-1)),Ie={class:"info-value"},Je={key:0,class:"empty-message"},Pe={key:1},qe={key:1,class:"empty-message"},Ae={key:2},Be={class:"config-item-row"},Fe={class:"config-item-label"},Ke=f(()=>o("strong",null,"类型：",-1)),Le={key:0},Ue=f(()=>o("strong",null,"描述：",-1)),Qe={key:1},ze=f(()=>o("strong",null,"必填：",-1)),Ge={key:2},He=f(()=>o("strong",null,"默认值：",-1)),We={class:"config-item-value"},Xe={key:0},Ye={key:1},Ze=["onClick"],et={class:"tool-description"},tt={key:0,class:"empty-schema"},ot={key:1},st={__name:"ConfigDetailView",setup(k){const $=ne(),A=ce(),w=V(()=>A.params.id),M=h(!1),e=h(null),x=h([]),R=h("basic"),y=h(!1),C=h({}),N=h(!1),p=V(()=>{if(!e.value||!e.value.conf_schema)return!1;try{const t=e.value.conf_schema;return t&&t.type==="object"&&t.properties}catch(t){return console.error("Error validating schema:",t),!1}}),S=V(()=>{if(!e.value||!e.value.conf_value)return{};try{return typeof e.value.conf_value=="string"?JSON.parse(e.value.conf_value):e.value.conf_value}catch(t){return console.error("Error parsing configuration value:",t),{}}}),B=V(()=>S.value&&Object.keys(S.value).length>0),F=t=>{if(!p.value||!e.value||!e.value.conf_schema||!e.value.conf_schema.properties)return t;const i=e.value.conf_schema.properties[t];return i&&i.title||t},K=t=>{if(!p.value||!e.value||!e.value.conf_schema||!e.value.conf_schema.properties)return"unknown";const i=e.value.conf_schema.properties[t];return i?i.type||"unknown":"object"},j=t=>{if(!p.value||!e.value||!e.value.conf_schema||!e.value.conf_schema.properties)return"";const i=e.value.conf_schema.properties[t];return i&&i.description||""},L=t=>{if(!p.value||!e.value||!e.value.conf_schema||!e.value.conf_schema.properties)return!1;const s=e.value.conf_schema;return s.properties[t]?s.required&&s.required.includes(t):!1},I=t=>{if(!p.value||!e.value||!e.value.conf_schema||!e.value.conf_schema.properties)return;const i=e.value.conf_schema.properties[t];if(i)return i.default},U=t=>typeof t=="object"&&t!==null;le(()=>{J(),Q()});const J=async()=>{M.value=!0;try{await D({method:"get",url:`/api/v1/config/${w.value}`,onSuccess:t=>{if(t&&t.conf_schema&&typeof t.conf_schema=="string")try{t.conf_schema=JSON.parse(t.conf_schema)}catch(s){console.error("Error parsing conf_schema:",s),t.conf_schema={}}e.value=t},errorMessage:"获取配置数据失败"})}catch(t){console.error("Error fetching config data:",t)}finally{M.value=!1}},Q=async()=>{try{await D({method:"get",url:`/api/v1/config/${w.value}/usage`,onSuccess:t=>{x.value=t.tools||[]},errorMessage:"获取关联工具失败"})}catch(t){console.error("Error fetching related tools:",t)}},z=()=>{if(!p.value){re.error("配置架构无效，无法进行配置");return}C.value={...S.value},y.value=!0},G=async()=>{N.value=!0;try{await D({method:"put",url:`/api/v1/config/${w.value}/value`,data:{conf_value:C.value},successMessage:"配置保存成功",errorMessage:"配置保存失败",onSuccess:()=>{y.value=!1,J()}})}finally{N.value=!1}};return(t,s)=>{const i=_("a-button"),H=_("a-space"),E=_("a-tab-pane"),O=_("a-empty"),W=_("a-alert"),X=_("a-tooltip"),Y=_("a-list-item-meta"),Z=_("a-list-item"),ee=_("a-list"),te=_("a-tabs"),oe=_("a-card"),se=_("a-modal");return a(),g(de,{"current-page-key":"config"},{default:n(()=>[e.value?(a(),g(oe,{key:0,title:`配置详情 - ${e.value?e.value.name:""}`},{extra:n(()=>[c(H,null,{default:n(()=>[c(i,{onClick:s[0]||(s[0]=l=>v($).push("/config"))},{icon:n(()=>[c(v(me))]),default:n(()=>[m(" 返回 ")]),_:1}),c(i,{type:"primary",onClick:s[1]||(s[1]=l=>v($).push(`/config/${w.value}/edit`))},{icon:n(()=>[c(v(ie))]),default:n(()=>[m(" 编辑 ")]),_:1}),c(i,{type:"primary",onClick:z,disabled:!p.value},{icon:n(()=>[c(v(ve))]),default:n(()=>[m(" 配置 ")]),_:1},8,["disabled"])]),_:1})]),default:n(()=>[c(te,{activeKey:R.value,"onUpdate:activeKey":s[2]||(s[2]=l=>R.value=l)},{default:n(()=>[c(E,{key:"basic",tab:"基本信息"},{default:n(()=>[o("div",ge,[o("div",ye,[be,o("div",ke,u(e.value.name),1)]),o("div",we,[Ce,o("div",Se,u(e.value.description||"无描述"),1)]),o("div",Oe,[Ve,o("div",$e,u(v(q)(e.value.created_at)),1)]),o("div",xe,[Ne,o("div",Ee,u(v(q)(e.value.updated_at)),1)]),o("div",Te,[De,o("div",Me,u(e.value.created_by||"-"),1)]),o("div",Re,[je,o("div",Ie,u(e.value.updated_by||"-"),1)])])]),_:1}),c(E,{key:"config",tab:"配置信息"},{default:n(()=>[e.value.conf_schema?(a(),r("div",Pe,[p.value?B.value?(a(),r("div",Ae,[(a(!0),r(T,null,P(S.value,(l,d)=>(a(),r("div",{key:d,class:"config-item"},[o("div",Be,[o("div",Fe,[m(u(F(d))+" ",1),e.value.value&&e.value.value.conf_schema&&e.value.value.conf_schema.properties&&e.value.value.conf_schema.properties[d]?(a(),g(X,{key:0,placement:"right"},{title:n(()=>[o("div",null,[o("div",null,[Ke,m(" "+u(K(d)),1)]),j(d)?(a(),r("div",Le,[Ue,m(" "+u(j(d)),1)])):b("",!0),L(d)?(a(),r("div",Qe,[ze,m(" 是")])):b("",!0),I(d)!==void 0?(a(),r("div",Ge,[He,m(" "+u(JSON.stringify(I(d))),1)])):b("",!0)])]),default:n(()=>[c(v(he),{class:"config-item-icon"})]),_:2},1024)):b("",!0)]),o("div",We,[U(l)?(a(),r("pre",Xe,u(JSON.stringify(l,null,2)),1)):(a(),r("span",Ye,u(l),1))])])]))),128))])):(a(),r("div",qe,[c(O,{description:"无配置值"})])):(a(),g(W,{key:0,type:"warning",message:"配置架构不是有效的JSON Schema格式",banner:"",style:{"margin-bottom":"16px"}}))])):(a(),r("div",Je,[c(O,{description:"无配置架构"})]))]),_:1}),c(E,{key:"tools",tab:"关联工具"},{default:n(()=>[x.value.length?(a(),g(ee,{key:1},{default:n(()=>[(a(!0),r(T,null,P(x.value,l=>(a(),g(Z,{key:l.id},{default:n(()=>[c(Y,null,{avatar:n(()=>[c(v(fe),{class:"tool-icon"})]),title:n(()=>[o("a",{class:"tool-link",onClick:d=>v($).push(`/tool/${l.id}`)},u(l.name),9,Ze)]),description:n(()=>[o("div",et,u(l.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(a(),g(O,{key:0,description:"无关联工具"}))]),_:1})]),_:1},8,["activeKey"])]),_:1},8,["title"])):b("",!0),c(se,{open:y.value,"onUpdate:open":s[5]||(s[5]=l=>y.value=l),title:`配置设置: ${e.value?e.value.name:""}`,width:"800px",footer:null,onCancel:s[6]||(s[6]=l=>y.value=!1),"ok-text":"确定","cancel-text":"取消"},{default:n(()=>[e.value?(a(),r(T,{key:0},[p.value?(a(),r("div",ot,[c(pe,{schema:e.value.conf_schema,value:C.value,"onUpdate:value":s[3]||(s[3]=l=>C.value=l),loading:N.value,onSubmit:G,onCancel:s[4]||(s[4]=l=>y.value=!1)},null,8,["schema","value","loading"])])):(a(),r("div",tt,[c(O,{description:"没有可用的配置模式"})]))],64)):b("",!0)]),_:1},8,["open","title"])]),_:1})}}},rt=ae(st,[["__scopeId","data-v-1fd9680a"]]);export{rt as default};
