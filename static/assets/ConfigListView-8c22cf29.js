import{_ as H,u as J,r as l,o as K,b as i,c,d as O,w as n,e as t,h as Q,f as u,P as W,g as h,k as p,t as $,E as X,x as Y,l as E,F as Z}from"./index-01e50f26.js";import{c as z,A as ee,f as ae,S as te}from"./AppLayout-94293e8c.js";import{_ as ne}from"./JsonSchemaForm-eb887394.js";const oe={class:"action-bar"},le=["onClick"],se={key:1},ie={key:2},ce={key:0,class:"empty-schema"},ue={key:1},re={__name:"ConfigListView",setup(de){const y=J(),k=l(!1),b=l([]),V=l(0),_=l(1),v=l(10),C=l(""),f=l(!1),s=l(null),w=l(!1),g=l({}),L=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160},{title:"操作",key:"action",width:200,fixed:"right"}];K(()=>{d()});const d=async()=>{k.value=!0;try{await z({method:"get",url:"/api/v1/config",params:{page:_.value,size:v.value,search:C.value||void 0},onSuccess:(o,e)=>{b.value=o,V.value=e.total},errorMessage:"获取配置列表失败"})}finally{k.value=!1}},P=()=>{_.value=1,d()},B=(o,e)=>{_.value=o,v.value=e,d()},F=(o,e)=>{_.value=1,v.value=e,d()},N=async o=>{try{await z({method:"delete",url:`/api/v1/config/${o}`,successMessage:"删除成功",errorMessage:"删除失败"}),d()}catch(e){console.error("Error deleting config:",e)}},T=o=>{s.value=o,g.value=o.conf_value||{},f.value=!0},x=()=>{f.value=!1,s.value=null,g.value={}},A=async o=>{if(s.value){w.value=!0;try{await z({method:"put",url:`/api/v1/config/${s.value.id}/value`,data:{conf_value:o},successMessage:"配置保存成功",errorMessage:"配置保存失败"}),d(),x()}catch(e){console.error("Error saving config value:",e)}finally{w.value=!1}}};return(o,e)=>{const D=i("a-input-search"),m=i("a-button"),I=i("a-popconfirm"),U=i("a-space"),j=i("a-table"),R=i("a-empty"),q=i("a-modal"),G=i("a-card");return c(),O(ee,{"current-page-key":"config"},{default:n(()=>[t(G,{title:"配置管理"},{default:n(()=>{var M;return[Q("div",oe,[t(D,{value:C.value,"onUpdate:value":e[0]||(e[0]=a=>C.value=a),placeholder:"搜索配置名称或描述",style:{width:"300px"},onSearch:P},null,8,["value"]),t(m,{type:"primary",onClick:e[1]||(e[1]=a=>u(y).push("/config/create"))},{icon:n(()=>[t(u(W))]),default:n(()=>[h(" 创建配置 ")]),_:1})]),t(j,{columns:L,"data-source":b.value,loading:k.value,pagination:{current:_.value,pageSize:v.value,total:V.value,onChange:B,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:F,showTotal:a=>`共 ${a} 条记录`},"row-key":a=>a.id},{bodyCell:n(({column:a,record:r})=>[a.key==="name"?(c(),p("a",{key:0,onClick:S=>u(y).push(`/config/${r.id}`)},$(r.name),9,le)):a.key==="description"?(c(),p("span",se,$(r.description||"无描述"),1)):a.key==="created_at"?(c(),p("span",ie,$(u(ae)(r.created_at)),1)):a.key==="action"?(c(),O(U,{key:3,size:0},{default:n(()=>[t(m,{type:"link",size:"small",onClick:S=>T(r)},{icon:n(()=>[t(u(te))]),default:n(()=>[h(" 配置 ")]),_:2},1032,["onClick"]),t(m,{type:"link",size:"small",onClick:S=>u(y).push(`/config/${r.id}/edit`)},{icon:n(()=>[t(u(X))]),default:n(()=>[h(" 编辑 ")]),_:2},1032,["onClick"]),t(I,{title:"确定要删除此配置吗？","ok-text":"确定","cancel-text":"取消",onConfirm:S=>N(r.id)},{default:n(()=>[t(m,{type:"link",size:"small",danger:""},{icon:n(()=>[t(u(Y))]),default:n(()=>[h(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):E("",!0)]),_:1},8,["data-source","loading","pagination","row-key"]),t(q,{open:f.value,"onUpdate:open":e[3]||(e[3]=a=>f.value=a),title:`配置设置: ${((M=s.value)==null?void 0:M.name)||""}`,width:"800px",footer:null,onCancel:x,"ok-text":"确定","cancel-text":"取消"},{default:n(()=>[s.value?(c(),p(Z,{key:0},[!s.value.conf_schema||Object.keys(s.value.conf_schema).length===0?(c(),p("div",ce,[t(R,{description:"没有可用的配置模式"})])):(c(),p("div",ue,[t(ne,{schema:s.value.conf_schema,value:g.value,"onUpdate:value":e[2]||(e[2]=a=>g.value=a),loading:w.value,onSubmit:A,onCancel:x},null,8,["schema","value","loading"])]))],64)):E("",!0)]),_:1},8,["open","title"])]}),_:1})]),_:1})}}},fe=H(re,[["__scopeId","data-v-ded3098c"]]);export{fe as default};
