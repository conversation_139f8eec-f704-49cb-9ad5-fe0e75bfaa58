import{u as T,r,o as A,b as l,c as s,d as g,w as a,e as n,h as D,f as c,P as I,g as h,k as d,t as f,F as L,E as R,x as U,l as j}from"./index-01e50f26.js";import{c as S,A as q,f as G}from"./AppLayout-94293e8c.js";const H={class:"action-bar"},J=["onClick"],K={key:1},Q={key:1},W={key:3},ee={__name:"FuncListView",setup(X){const v=T(),m=r(!1),w=r([]),C=r(0),u=r(1),_=r(10),y=r(""),z=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"版本",key:"version",width:100,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:200,fixed:"right"}];A(()=>{p()});const p=async()=>{m.value=!0;try{await S({method:"get",url:"/api/v1/func",params:{page:u.value,size:_.value,search:y.value||void 0},onSuccess:(i,e)=>{w.value=i,C.value=e.total},errorMessage:"获取函数列表失败"})}finally{m.value=!1}},b=()=>{u.value=1,p()},$=(i,e)=>{u.value=i,_.value=e,p()},P=(i,e)=>{u.value=1,_.value=e,p()},V=async i=>{try{await S({method:"delete",url:`/api/v1/func/${i}`,successMessage:"删除成功",errorMessage:"删除失败"}),p()}catch(e){console.error("Error deleting function:",e)}};return(i,e)=>{const B=l("a-input-search"),k=l("a-button"),E=l("a-tag"),F=l("a-popconfirm"),M=l("a-space"),N=l("a-table"),O=l("a-card");return s(),g(q,{"current-page-key":"func"},{default:a(()=>[n(O,{title:"函数管理"},{default:a(()=>[D("div",H,[n(B,{value:y.value,"onUpdate:value":e[0]||(e[0]=t=>y.value=t),placeholder:"搜索函数名称或描述",style:{width:"300px"},onSearch:b},null,8,["value"]),n(k,{type:"primary",onClick:e[1]||(e[1]=t=>c(v).push("/func/create"))},{icon:a(()=>[n(c(I))]),default:a(()=>[h(" 创建函数 ")]),_:1})]),n(N,{columns:z,"data-source":w.value,loading:m.value,pagination:{current:u.value,pageSize:_.value,total:C.value,onChange:$,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:P,showTotal:t=>`共 ${t} 条记录`},"row-key":t=>t.id},{bodyCell:a(({column:t,record:o})=>[t.key==="name"?(s(),d("a",{key:0,onClick:x=>c(v).push(`/func/${o.id}`)},f(o.name),9,J)):t.key==="description"?(s(),d("span",K,f(o.description||"无描述"),1)):t.key==="version"?(s(),d(L,{key:2},[o.current_version?(s(),g(E,{key:0,color:"blue"},{default:a(()=>[h("v"+f(o.current_version),1)]),_:2},1024)):(s(),d("span",Q,"未发布"))],64)):t.key==="created_at"?(s(),d("span",W,f(c(G)(o.created_at)),1)):t.key==="action"?(s(),g(M,{key:4,size:0},{default:a(()=>[n(k,{type:"link",size:"small",onClick:x=>c(v).push(`/func/${o.id}/edit`)},{icon:a(()=>[n(c(R))]),default:a(()=>[h(" 编辑 ")]),_:2},1032,["onClick"]),n(F,{title:"确定要删除此函数吗？","ok-text":"确定","cancel-text":"取消",onConfirm:x=>V(o.id)},{default:a(()=>[n(k,{type:"link",size:"small",danger:""},{icon:a(()=>[n(c(U))]),default:a(()=>[h(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):j("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1})]),_:1})}}};export{ee as default};
