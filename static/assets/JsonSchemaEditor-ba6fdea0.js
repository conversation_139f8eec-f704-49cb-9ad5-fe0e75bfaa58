import{_ as mn}from"./MonacoEditor-e8f3b13e.js";import{e as h,A as gn,b as B,T as vn,G as bn,H as kt,n as $t,_ as yn,r as be,z as Pt,o as _n,B as At,q as it,c as V,k as ye,h as he,w as m,g as O,f as Pe,P as wn,d as _e,t as Ye,l as se,E as En,x as Dn,F as lt,p as Sn,m as xn}from"./index-01e50f26.js";var Cn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"};const In=Cn;function Nt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?Object(arguments[e]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),o.forEach(function(r){On(t,r,n[r])})}return t}function On(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var St=function(e,n){var o=Nt({},e,n.attrs);return h(gn,Nt({},o,{icon:In}),null)};St.displayName="MenuOutlined";St.inheritAttrs=!1;const Tn=St;/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Ft(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,o)}return n}function re(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ft(Object(n),!0).forEach(function(o){kn(t,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(n,o))})}return t}function We(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?We=function(e){return typeof e}:We=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(t)}function kn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function de(){return de=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},de.apply(this,arguments)}function Pn(t,e){if(t==null)return{};var n={},o=Object.keys(t),r,a;for(a=0;a<o.length;a++)r=o[a],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}function An(t,e){if(t==null)return{};var n=Pn(t,e),o,r;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(r=0;r<a.length;r++)o=a[r],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(t,o)&&(n[o]=t[o])}return n}var Nn="1.14.0";function ue(t){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(t)}var ce=ue(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ue=ue(/Edge/i),Mt=ue(/firefox/i),Me=ue(/safari/i)&&!ue(/chrome/i)&&!ue(/android/i),zt=ue(/iP(ad|od|hone)/i),Fn=ue(/chrome/i)&&ue(/android/i),Wt={capture:!1,passive:!1};function C(t,e,n){t.addEventListener(e,n,!ce&&Wt)}function D(t,e,n){t.removeEventListener(e,n,!ce&&Wt)}function Qe(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch{return!1}return!1}}function Mn(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function ne(t,e,n,o){if(t){n=n||document;do{if(e!=null&&(e[0]===">"?t.parentNode===n&&Qe(t,e):Qe(t,e))||o&&t===n)return t;if(t===n)break}while(t=Mn(t))}return null}var Lt=/\s+/g;function z(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(Lt," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(Lt," ")}}function g(t,e,n){var o=t&&t.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),e===void 0?n:n[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=n+(typeof n=="string"?"":"px")}}function Te(t,e){var n="";if(typeof t=="string")n=t;else do{var o=g(t,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function Gt(t,e,n){if(t){var o=t.getElementsByTagName(e),r=0,a=o.length;if(n)for(;r<a;r++)n(o[r],r);return o}return[]}function oe(){var t=document.scrollingElement;return t||document.documentElement}function F(t,e,n,o,r){if(!(!t.getBoundingClientRect&&t!==window)){var a,l,s,i,u,c,f;if(t!==window&&t.parentNode&&t!==oe()?(a=t.getBoundingClientRect(),l=a.top,s=a.left,i=a.bottom,u=a.right,c=a.height,f=a.width):(l=0,s=0,i=window.innerHeight,u=window.innerWidth,c=window.innerHeight,f=window.innerWidth),(e||n)&&t!==window&&(r=r||t.parentNode,!ce))do if(r&&r.getBoundingClientRect&&(g(r,"transform")!=="none"||n&&g(r,"position")!=="static")){var w=r.getBoundingClientRect();l-=w.top+parseInt(g(r,"border-top-width")),s-=w.left+parseInt(g(r,"border-left-width")),i=l+a.height,u=s+a.width;break}while(r=r.parentNode);if(o&&t!==window){var S=Te(r||t),E=S&&S.a,x=S&&S.d;S&&(l/=x,s/=E,f/=E,c/=x,i=l+c,u=s+f)}return{top:l,left:s,bottom:i,right:u,width:f,height:c}}}function Rt(t,e,n){for(var o=ge(t,!0),r=F(t)[e];o;){var a=F(o)[n],l=void 0;if(n==="top"||n==="left"?l=r>=a:l=r<=a,!l)return o;if(o===oe())break;o=ge(o,!1)}return!1}function ke(t,e,n,o){for(var r=0,a=0,l=t.children;a<l.length;){if(l[a].style.display!=="none"&&l[a]!==v.ghost&&(o||l[a]!==v.dragged)&&ne(l[a],n.draggable,t,!1)){if(r===e)return l[a];r++}a++}return null}function xt(t,e){for(var n=t.lastElementChild;n&&(n===v.ghost||g(n,"display")==="none"||e&&!Qe(n,e));)n=n.previousElementSibling;return n||null}function Z(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==v.clone&&(!e||Qe(t,e))&&n++;return n}function jt(t){var e=0,n=0,o=oe();if(t)do{var r=Te(t),a=r.a,l=r.d;e+=t.scrollLeft*a,n+=t.scrollTop*l}while(t!==o&&(t=t.parentNode));return[e,n]}function Ln(t,e){for(var n in t)if(t.hasOwnProperty(n)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n)}return-1}function ge(t,e){if(!t||!t.getBoundingClientRect)return oe();var n=t,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=g(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return oe();if(o||e)return n;o=!0}}while(n=n.parentNode);return oe()}function Rn(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function st(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Le;function Jt(t,e){return function(){if(!Le){var n=arguments,o=this;n.length===1?t.call(o,n[0]):t.apply(o,n),Le=setTimeout(function(){Le=void 0},e)}}}function jn(){clearTimeout(Le),Le=void 0}function Kt(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function Zt(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}var G="Sortable"+new Date().getTime();function Bn(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(r){if(!(g(r,"display")==="none"||r===v.ghost)){t.push({target:r,rect:F(r)});var a=re({},t[t.length-1].rect);if(r.thisAnimationDuration){var l=Te(r,!0);l&&(a.top-=l.f,a.left-=l.e)}r.fromRect=a}})}},addAnimationState:function(o){t.push(o)},removeAnimationState:function(o){t.splice(Ln(t,{target:o}),1)},animateAll:function(o){var r=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var a=!1,l=0;t.forEach(function(s){var i=0,u=s.target,c=u.fromRect,f=F(u),w=u.prevFromRect,S=u.prevToRect,E=s.rect,x=Te(u,!0);x&&(f.top-=x.f,f.left-=x.e),u.toRect=f,u.thisAnimationDuration&&st(w,f)&&!st(c,f)&&(E.top-f.top)/(E.left-f.left)===(c.top-f.top)/(c.left-f.left)&&(i=Xn(E,w,S,r.options)),st(f,c)||(u.prevFromRect=c,u.prevToRect=f,i||(i=r.options.animation),r.animate(u,E,f,i)),i&&(a=!0,l=Math.max(l,i),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},i),u.thisAnimationDuration=i)}),clearTimeout(e),a?e=setTimeout(function(){typeof o=="function"&&o()},l):typeof o=="function"&&o(),t=[]},animate:function(o,r,a,l){if(l){g(o,"transition",""),g(o,"transform","");var s=Te(this.el),i=s&&s.a,u=s&&s.d,c=(r.left-a.left)/(i||1),f=(r.top-a.top)/(u||1);o.animatingX=!!c,o.animatingY=!!f,g(o,"transform","translate3d("+c+"px,"+f+"px,0)"),this.forRepaintDummy=Un(o),g(o,"transition","transform "+l+"ms"+(this.options.easing?" "+this.options.easing:"")),g(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){g(o,"transition",""),g(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},l)}}}}function Un(t){return t.offsetWidth}function Xn(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}var xe=[],ut={initializeByDefault:!0},Xe={mount:function(e){for(var n in ut)ut.hasOwnProperty(n)&&!(n in e)&&(e[n]=ut[n]);xe.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),xe.push(e)},pluginEvent:function(e,n,o){var r=this;this.eventCanceled=!1,o.cancel=function(){r.eventCanceled=!0};var a=e+"Global";xe.forEach(function(l){n[l.pluginName]&&(n[l.pluginName][a]&&n[l.pluginName][a](re({sortable:n},o)),n.options[l.pluginName]&&n[l.pluginName][e]&&n[l.pluginName][e](re({sortable:n},o)))})},initializePlugins:function(e,n,o,r){xe.forEach(function(s){var i=s.pluginName;if(!(!e.options[i]&&!s.initializeByDefault)){var u=new s(e,n,e.options);u.sortable=e,u.options=e.options,e[i]=u,de(o,u.defaults)}});for(var a in e.options)if(e.options.hasOwnProperty(a)){var l=this.modifyOption(e,a,e.options[a]);typeof l<"u"&&(e.options[a]=l)}},getEventProperties:function(e,n){var o={};return xe.forEach(function(r){typeof r.eventProperties=="function"&&de(o,r.eventProperties.call(n[r.pluginName],e))}),o},modifyOption:function(e,n,o){var r;return xe.forEach(function(a){e[a.pluginName]&&a.optionListeners&&typeof a.optionListeners[n]=="function"&&(r=a.optionListeners[n].call(e[a.pluginName],o))}),r}};function Hn(t){var e=t.sortable,n=t.rootEl,o=t.name,r=t.targetEl,a=t.cloneEl,l=t.toEl,s=t.fromEl,i=t.oldIndex,u=t.newIndex,c=t.oldDraggableIndex,f=t.newDraggableIndex,w=t.originalEvent,S=t.putSortable,E=t.extraEventProperties;if(e=e||n&&n[G],!!e){var x,$=e.options,Q="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!ce&&!Ue?x=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(x=document.createEvent("Event"),x.initEvent(o,!0,!0)),x.to=l||n,x.from=s||n,x.item=r||n,x.clone=a,x.oldIndex=i,x.newIndex=u,x.oldDraggableIndex=c,x.newDraggableIndex=f,x.originalEvent=w,x.pullMode=S?S.lastPutMode:void 0;var M=re(re({},E),Xe.getEventProperties(o,e));for(var H in M)x[H]=M[H];n&&n.dispatchEvent(x),$[Q]&&$[Q].call(e,x)}}var Yn=["evt"],q=function(e,n){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=o.evt,a=An(o,Yn);Xe.pluginEvent.bind(v)(e,n,re({dragEl:d,parentEl:P,ghostEl:_,rootEl:T,nextEl:De,lastDownEl:Ge,cloneEl:A,cloneHidden:me,dragStarted:Ae,putSortable:R,activeSortable:v.active,originalEvent:r,oldIndex:Oe,oldDraggableIndex:Re,newIndex:W,newDraggableIndex:pe,hideGhostForTarget:nn,unhideGhostForTarget:on,cloneNowHidden:function(){me=!0},cloneNowShown:function(){me=!1},dispatchSortableEvent:function(s){X({sortable:n,name:s,originalEvent:r})}},a))};function X(t){Hn(re({putSortable:R,cloneEl:A,targetEl:d,rootEl:T,oldIndex:Oe,oldDraggableIndex:Re,newIndex:W,newDraggableIndex:pe},t))}var d,P,_,T,De,Ge,A,me,Oe,W,Re,pe,Ve,R,Ie=!1,et=!1,tt=[],we,ee,dt,ct,Bt,Ut,Ae,Ce,je,Be=!1,qe=!1,Je,j,ft=[],yt=!1,nt=[],rt=typeof document<"u",$e=zt,Xt=Ue||ce?"cssFloat":"float",Vn=rt&&!Fn&&!zt&&"draggable"in document.createElement("div"),Qt=function(){if(rt){if(ce)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),en=function(e,n){var o=g(e),r=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),a=ke(e,0,n),l=ke(e,1,n),s=a&&g(a),i=l&&g(l),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+F(a).width,c=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+F(l).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&s.float&&s.float!=="none"){var f=s.float==="left"?"left":"right";return l&&(i.clear==="both"||i.clear===f)?"vertical":"horizontal"}return a&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=r&&o[Xt]==="none"||l&&o[Xt]==="none"&&u+c>r)?"vertical":"horizontal"},qn=function(e,n,o){var r=o?e.left:e.top,a=o?e.right:e.bottom,l=o?e.width:e.height,s=o?n.left:n.top,i=o?n.right:n.bottom,u=o?n.width:n.height;return r===s||a===i||r+l/2===s+u/2},$n=function(e,n){var o;return tt.some(function(r){var a=r[G].options.emptyInsertThreshold;if(!(!a||xt(r))){var l=F(r),s=e>=l.left-a&&e<=l.right+a,i=n>=l.top-a&&n<=l.bottom+a;if(s&&i)return o=r}}),o},tn=function(e){function n(a,l){return function(s,i,u,c){var f=s.options.group.name&&i.options.group.name&&s.options.group.name===i.options.group.name;if(a==null&&(l||f))return!0;if(a==null||a===!1)return!1;if(l&&a==="clone")return a;if(typeof a=="function")return n(a(s,i,u,c),l)(s,i,u,c);var w=(l?s:i).options.group.name;return a===!0||typeof a=="string"&&a===w||a.join&&a.indexOf(w)>-1}}var o={},r=e.group;(!r||We(r)!="object")&&(r={name:r}),o.name=r.name,o.checkPull=n(r.pull,!0),o.checkPut=n(r.put),o.revertClone=r.revertClone,e.group=o},nn=function(){!Qt&&_&&g(_,"display","none")},on=function(){!Qt&&_&&g(_,"display","")};rt&&document.addEventListener("click",function(t){if(et)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),et=!1,!1},!0);var Ee=function(e){if(d){e=e.touches?e.touches[0]:e;var n=$n(e.clientX,e.clientY);if(n){var o={};for(var r in e)e.hasOwnProperty(r)&&(o[r]=e[r]);o.target=o.rootEl=n,o.preventDefault=void 0,o.stopPropagation=void 0,n[G]._onDragOver(o)}}},zn=function(e){d&&d.parentNode[G]._isOutsideThisEl(e.target)};function v(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=de({},e),t[G]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return en(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(l,s){l.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:v.supportPointer!==!1&&"PointerEvent"in window&&!Me,emptyInsertThreshold:5};Xe.initializePlugins(this,t,n);for(var o in n)!(o in e)&&(e[o]=n[o]);tn(e);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=e.forceFallback?!1:Vn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?C(t,"pointerdown",this._onTapStart):(C(t,"mousedown",this._onTapStart),C(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(C(t,"dragover",this),C(t,"dragenter",this)),tt.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),de(this,Bn())}v.prototype={constructor:v,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Ce=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,d):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,o=this.el,r=this.options,a=r.preventOnFilter,l=e.type,s=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,i=(s||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||i,c=r.filter;if(to(o),!d&&!(/mousedown|pointerdown/.test(l)&&e.button!==0||r.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Me&&i&&i.tagName.toUpperCase()==="SELECT")&&(i=ne(i,r.draggable,o,!1),!(i&&i.animated)&&Ge!==i)){if(Oe=Z(i),Re=Z(i,r.draggable),typeof c=="function"){if(c.call(this,e,i,this)){X({sortable:n,rootEl:u,name:"filter",targetEl:i,toEl:o,fromEl:o}),q("filter",n,{evt:e}),a&&e.cancelable&&e.preventDefault();return}}else if(c&&(c=c.split(",").some(function(f){if(f=ne(u,f.trim(),o,!1),f)return X({sortable:n,rootEl:f,name:"filter",targetEl:i,fromEl:o,toEl:o}),q("filter",n,{evt:e}),!0}),c)){a&&e.cancelable&&e.preventDefault();return}r.handle&&!ne(u,r.handle,o,!1)||this._prepareDragStart(e,s,i)}}},_prepareDragStart:function(e,n,o){var r=this,a=r.el,l=r.options,s=a.ownerDocument,i;if(o&&!d&&o.parentNode===a){var u=F(o);if(T=a,d=o,P=d.parentNode,De=d.nextSibling,Ge=o,Ve=l.group,v.dragged=d,we={target:d,clientX:(n||e).clientX,clientY:(n||e).clientY},Bt=we.clientX-u.left,Ut=we.clientY-u.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,d.style["will-change"]="all",i=function(){if(q("delayEnded",r,{evt:e}),v.eventCanceled){r._onDrop();return}r._disableDelayedDragEvents(),!Mt&&r.nativeDraggable&&(d.draggable=!0),r._triggerDragStart(e,n),X({sortable:r,name:"choose",originalEvent:e}),z(d,l.chosenClass,!0)},l.ignore.split(",").forEach(function(c){Gt(d,c.trim(),ht)}),C(s,"dragover",Ee),C(s,"mousemove",Ee),C(s,"touchmove",Ee),C(s,"mouseup",r._onDrop),C(s,"touchend",r._onDrop),C(s,"touchcancel",r._onDrop),Mt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,d.draggable=!0),q("delayStart",this,{evt:e}),l.delay&&(!l.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Ue||ce))){if(v.eventCanceled){this._onDrop();return}C(s,"mouseup",r._disableDelayedDrag),C(s,"touchend",r._disableDelayedDrag),C(s,"touchcancel",r._disableDelayedDrag),C(s,"mousemove",r._delayedDragTouchMoveHandler),C(s,"touchmove",r._delayedDragTouchMoveHandler),l.supportPointer&&C(s,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(i,l.delay)}else i()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){d&&ht(d),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;D(e,"mouseup",this._disableDelayedDrag),D(e,"touchend",this._disableDelayedDrag),D(e,"touchcancel",this._disableDelayedDrag),D(e,"mousemove",this._delayedDragTouchMoveHandler),D(e,"touchmove",this._delayedDragTouchMoveHandler),D(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?C(document,"pointermove",this._onTouchMove):n?C(document,"touchmove",this._onTouchMove):C(document,"mousemove",this._onTouchMove):(C(d,"dragend",this),C(T,"dragstart",this._onDragStart));try{document.selection?Ke(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,n){if(Ie=!1,T&&d){q("dragStarted",this,{evt:n}),this.nativeDraggable&&C(document,"dragover",zn);var o=this.options;!e&&z(d,o.dragClass,!1),z(d,o.ghostClass,!0),v.active=this,e&&this._appendGhost(),X({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(ee){this._lastX=ee.clientX,this._lastY=ee.clientY,nn();for(var e=document.elementFromPoint(ee.clientX,ee.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(ee.clientX,ee.clientY),e!==n);)n=e;if(d.parentNode[G]._isOutsideThisEl(e),n)do{if(n[G]){var o=void 0;if(o=n[G]._onDragOver({clientX:ee.clientX,clientY:ee.clientY,target:e,rootEl:n}),o&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);on()}},_onTouchMove:function(e){if(we){var n=this.options,o=n.fallbackTolerance,r=n.fallbackOffset,a=e.touches?e.touches[0]:e,l=_&&Te(_,!0),s=_&&l&&l.a,i=_&&l&&l.d,u=$e&&j&&jt(j),c=(a.clientX-we.clientX+r.x)/(s||1)+(u?u[0]-ft[0]:0)/(s||1),f=(a.clientY-we.clientY+r.y)/(i||1)+(u?u[1]-ft[1]:0)/(i||1);if(!v.active&&!Ie){if(o&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(_){l?(l.e+=c-(dt||0),l.f+=f-(ct||0)):l={a:1,b:0,c:0,d:1,e:c,f};var w="matrix(".concat(l.a,",").concat(l.b,",").concat(l.c,",").concat(l.d,",").concat(l.e,",").concat(l.f,")");g(_,"webkitTransform",w),g(_,"mozTransform",w),g(_,"msTransform",w),g(_,"transform",w),dt=c,ct=f,ee=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!_){var e=this.options.fallbackOnBody?document.body:T,n=F(d,!0,$e,!0,e),o=this.options;if($e){for(j=e;g(j,"position")==="static"&&g(j,"transform")==="none"&&j!==document;)j=j.parentNode;j!==document.body&&j!==document.documentElement?(j===document&&(j=oe()),n.top+=j.scrollTop,n.left+=j.scrollLeft):j=oe(),ft=jt(j)}_=d.cloneNode(!0),z(_,o.ghostClass,!1),z(_,o.fallbackClass,!0),z(_,o.dragClass,!0),g(_,"transition",""),g(_,"transform",""),g(_,"box-sizing","border-box"),g(_,"margin",0),g(_,"top",n.top),g(_,"left",n.left),g(_,"width",n.width),g(_,"height",n.height),g(_,"opacity","0.8"),g(_,"position",$e?"absolute":"fixed"),g(_,"zIndex","100000"),g(_,"pointerEvents","none"),v.ghost=_,e.appendChild(_),g(_,"transform-origin",Bt/parseInt(_.style.width)*100+"% "+Ut/parseInt(_.style.height)*100+"%")}},_onDragStart:function(e,n){var o=this,r=e.dataTransfer,a=o.options;if(q("dragStart",this,{evt:e}),v.eventCanceled){this._onDrop();return}q("setupClone",this),v.eventCanceled||(A=Zt(d),A.draggable=!1,A.style["will-change"]="",this._hideClone(),z(A,this.options.chosenClass,!1),v.clone=A),o.cloneId=Ke(function(){q("clone",o),!v.eventCanceled&&(o.options.removeCloneOnHide||T.insertBefore(A,d),o._hideClone(),X({sortable:o,name:"clone"}))}),!n&&z(d,a.dragClass,!0),n?(et=!0,o._loopId=setInterval(o._emulateDragOver,50)):(D(document,"mouseup",o._onDrop),D(document,"touchend",o._onDrop),D(document,"touchcancel",o._onDrop),r&&(r.effectAllowed="move",a.setData&&a.setData.call(o,r,d)),C(document,"drop",o),g(d,"transform","translateZ(0)")),Ie=!0,o._dragStartId=Ke(o._dragStarted.bind(o,n,e)),C(document,"selectstart",o),Ae=!0,Me&&g(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,o=e.target,r,a,l,s=this.options,i=s.group,u=v.active,c=Ve===i,f=s.sort,w=R||u,S,E=this,x=!1;if(yt)return;function $(fe,ve){q(fe,E,re({evt:e,isOwner:c,axis:S?"vertical":"horizontal",revert:l,dragRect:r,targetRect:a,canSort:f,fromSortable:w,target:o,completed:M,onMove:function(He,at){return ze(T,n,d,r,He,F(He),e,at)},changed:H},ve))}function Q(){$("dragOverAnimationCapture"),E.captureAnimationState(),E!==w&&w.captureAnimationState()}function M(fe){return $("dragOverCompleted",{insertion:fe}),fe&&(c?u._hideClone():u._showClone(E),E!==w&&(z(d,R?R.options.ghostClass:u.options.ghostClass,!1),z(d,s.ghostClass,!0)),R!==E&&E!==v.active?R=E:E===v.active&&R&&(R=null),w===E&&(E._ignoreWhileAnimating=o),E.animateAll(function(){$("dragOverAnimationComplete"),E._ignoreWhileAnimating=null}),E!==w&&(w.animateAll(),w._ignoreWhileAnimating=null)),(o===d&&!d.animated||o===n&&!o.animated)&&(Ce=null),!s.dragoverBubble&&!e.rootEl&&o!==document&&(d.parentNode[G]._isOutsideThisEl(e.target),!fe&&Ee(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),x=!0}function H(){W=Z(d),pe=Z(d,s.draggable),X({sortable:E,name:"change",toEl:n,newIndex:W,newDraggableIndex:pe,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=ne(o,s.draggable,n,!0),$("dragOver"),v.eventCanceled)return x;if(d.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||E._ignoreWhileAnimating===o)return M(!1);if(et=!1,u&&!s.disabled&&(c?f||(l=P!==T):R===this||(this.lastPutMode=Ve.checkPull(this,u,d,e))&&i.checkPut(this,u,d,e))){if(S=this._getDirection(e,o)==="vertical",r=F(d),$("dragOverValid"),v.eventCanceled)return x;if(l)return P=T,Q(),this._hideClone(),$("revert"),v.eventCanceled||(De?T.insertBefore(d,De):T.appendChild(d)),M(!0);var J=xt(n,s.draggable);if(!J||Kn(e,S,this)&&!J.animated){if(J===d)return M(!1);if(J&&n===e.target&&(o=J),o&&(a=F(o)),ze(T,n,d,r,o,a,e,!!o)!==!1)return Q(),n.appendChild(d),P=n,H(),M(!0)}else if(J&&Jn(e,S,this)){var ae=ke(n,0,s,!0);if(ae===d)return M(!1);if(o=ae,a=F(o),ze(T,n,d,r,o,a,e,!1)!==!1)return Q(),n.insertBefore(d,ae),P=n,H(),M(!0)}else if(o.parentNode===n){a=F(o);var y=0,p,L=d.parentNode!==n,U=!qn(d.animated&&d.toRect||r,o.animated&&o.toRect||a,S),ie=S?"top":"left",te=Rt(o,"top","top")||Rt(d,"top","top"),le=te?te.scrollTop:void 0;Ce!==o&&(p=a[ie],Be=!1,qe=!U&&s.invertSwap||L),y=Zn(e,o,a,S,U?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,qe,Ce===o);var K;if(y!==0){var k=Z(d);do k-=y,K=P.children[k];while(K&&(g(K,"display")==="none"||K===_))}if(y===0||K===o)return M(!1);Ce=o,je=y;var I=o.nextElementSibling,Y=!1;Y=y===1;var Se=ze(T,n,d,r,o,a,e,Y);if(Se!==!1)return(Se===1||Se===-1)&&(Y=Se===1),yt=!0,setTimeout(Gn,30),Q(),Y&&!I?n.appendChild(d):o.parentNode.insertBefore(d,Y?I:o),te&&Kt(te,0,le-te.scrollTop),P=d.parentNode,p!==void 0&&!qe&&(Je=Math.abs(p-F(o)[ie])),H(),M(!0)}if(n.contains(d))return M(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){D(document,"mousemove",this._onTouchMove),D(document,"touchmove",this._onTouchMove),D(document,"pointermove",this._onTouchMove),D(document,"dragover",Ee),D(document,"mousemove",Ee),D(document,"touchmove",Ee)},_offUpEvents:function(){var e=this.el.ownerDocument;D(e,"mouseup",this._onDrop),D(e,"touchend",this._onDrop),D(e,"pointerup",this._onDrop),D(e,"touchcancel",this._onDrop),D(document,"selectstart",this)},_onDrop:function(e){var n=this.el,o=this.options;if(W=Z(d),pe=Z(d,o.draggable),q("drop",this,{evt:e}),P=d&&d.parentNode,W=Z(d),pe=Z(d,o.draggable),v.eventCanceled){this._nulling();return}Ie=!1,qe=!1,Be=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),_t(this.cloneId),_t(this._dragStartId),this.nativeDraggable&&(D(document,"drop",this),D(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Me&&g(document.body,"user-select",""),g(d,"transform",""),e&&(Ae&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),_&&_.parentNode&&_.parentNode.removeChild(_),(T===P||R&&R.lastPutMode!=="clone")&&A&&A.parentNode&&A.parentNode.removeChild(A),d&&(this.nativeDraggable&&D(d,"dragend",this),ht(d),d.style["will-change"]="",Ae&&!Ie&&z(d,R?R.options.ghostClass:this.options.ghostClass,!1),z(d,this.options.chosenClass,!1),X({sortable:this,name:"unchoose",toEl:P,newIndex:null,newDraggableIndex:null,originalEvent:e}),T!==P?(W>=0&&(X({rootEl:P,name:"add",toEl:P,fromEl:T,originalEvent:e}),X({sortable:this,name:"remove",toEl:P,originalEvent:e}),X({rootEl:P,name:"sort",toEl:P,fromEl:T,originalEvent:e}),X({sortable:this,name:"sort",toEl:P,originalEvent:e})),R&&R.save()):W!==Oe&&W>=0&&(X({sortable:this,name:"update",toEl:P,originalEvent:e}),X({sortable:this,name:"sort",toEl:P,originalEvent:e})),v.active&&((W==null||W===-1)&&(W=Oe,pe=Re),X({sortable:this,name:"end",toEl:P,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){q("nulling",this),T=d=P=_=De=A=Ge=me=we=ee=Ae=W=pe=Oe=Re=Ce=je=R=Ve=v.dragged=v.ghost=v.clone=v.active=null,nt.forEach(function(e){e.checked=!0}),nt.length=dt=ct=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":d&&(this._onDragOver(e),Wn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,o=this.el.children,r=0,a=o.length,l=this.options;r<a;r++)n=o[r],ne(n,l.draggable,this.el,!1)&&e.push(n.getAttribute(l.dataIdAttr)||eo(n));return e},sort:function(e,n){var o={},r=this.el;this.toArray().forEach(function(a,l){var s=r.children[l];ne(s,this.options.draggable,r,!1)&&(o[a]=s)},this),n&&this.captureAnimationState(),e.forEach(function(a){o[a]&&(r.removeChild(o[a]),r.appendChild(o[a]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return ne(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var o=this.options;if(n===void 0)return o[e];var r=Xe.modifyOption(this,e,n);typeof r<"u"?o[e]=r:o[e]=n,e==="group"&&tn(o)},destroy:function(){q("destroy",this);var e=this.el;e[G]=null,D(e,"mousedown",this._onTapStart),D(e,"touchstart",this._onTapStart),D(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(D(e,"dragover",this),D(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),tt.splice(tt.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!me){if(q("hideClone",this),v.eventCanceled)return;g(A,"display","none"),this.options.removeCloneOnHide&&A.parentNode&&A.parentNode.removeChild(A),me=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(me){if(q("showClone",this),v.eventCanceled)return;d.parentNode==T&&!this.options.group.revertClone?T.insertBefore(A,d):De?T.insertBefore(A,De):T.appendChild(A),this.options.group.revertClone&&this.animate(d,A),g(A,"display",""),me=!1}}};function Wn(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function ze(t,e,n,o,r,a,l,s){var i,u=t[G],c=u.options.onMove,f;return window.CustomEvent&&!ce&&!Ue?i=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(i=document.createEvent("Event"),i.initEvent("move",!0,!0)),i.to=e,i.from=t,i.dragged=n,i.draggedRect=o,i.related=r||e,i.relatedRect=a||F(e),i.willInsertAfter=s,i.originalEvent=l,t.dispatchEvent(i),c&&(f=c.call(u,i,l)),f}function ht(t){t.draggable=!1}function Gn(){yt=!1}function Jn(t,e,n){var o=F(ke(n.el,0,n.options,!0)),r=10;return e?t.clientX<o.left-r||t.clientY<o.top&&t.clientX<o.right:t.clientY<o.top-r||t.clientY<o.bottom&&t.clientX<o.left}function Kn(t,e,n){var o=F(xt(n.el,n.options.draggable)),r=10;return e?t.clientX>o.right+r||t.clientX<=o.right&&t.clientY>o.bottom&&t.clientX>=o.left:t.clientX>o.right&&t.clientY>o.top||t.clientX<=o.right&&t.clientY>o.bottom+r}function Zn(t,e,n,o,r,a,l,s){var i=o?t.clientY:t.clientX,u=o?n.height:n.width,c=o?n.top:n.left,f=o?n.bottom:n.right,w=!1;if(!l){if(s&&Je<u*r){if(!Be&&(je===1?i>c+u*a/2:i<f-u*a/2)&&(Be=!0),Be)w=!0;else if(je===1?i<c+Je:i>f-Je)return-je}else if(i>c+u*(1-r)/2&&i<f-u*(1-r)/2)return Qn(e)}return w=w||l,w&&(i<c+u*a/2||i>f-u*a/2)?i>c+u/2?1:-1:0}function Qn(t){return Z(d)<Z(t)?1:-1}function eo(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function to(t){nt.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&nt.push(o)}}function Ke(t){return setTimeout(t,0)}function _t(t){return clearTimeout(t)}rt&&C(document,"touchmove",function(t){(v.active||Ie)&&t.cancelable&&t.preventDefault()});v.utils={on:C,off:D,css:g,find:Gt,is:function(e,n){return!!ne(e,n,e,!1)},extend:Rn,throttle:Jt,closest:ne,toggleClass:z,clone:Zt,index:Z,nextTick:Ke,cancelNextTick:_t,detectDirection:en,getChild:ke};v.get=function(t){return t[G]};v.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(v.utils=re(re({},v.utils),o.utils)),Xe.mount(o)})};v.create=function(t,e){return new v(t,e)};v.version=Nn;var N=[],Ne,wt,Et=!1,pt,mt,ot,Fe;function no(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(n){var o=n.originalEvent;this.sortable.nativeDraggable?C(document,"dragover",this._handleAutoScroll):this.options.supportPointer?C(document,"pointermove",this._handleFallbackAutoScroll):o.touches?C(document,"touchmove",this._handleFallbackAutoScroll):C(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var o=n.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?D(document,"dragover",this._handleAutoScroll):(D(document,"pointermove",this._handleFallbackAutoScroll),D(document,"touchmove",this._handleFallbackAutoScroll),D(document,"mousemove",this._handleFallbackAutoScroll)),Ht(),Ze(),jn()},nulling:function(){ot=wt=Ne=Et=Fe=pt=mt=null,N.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,o){var r=this,a=(n.touches?n.touches[0]:n).clientX,l=(n.touches?n.touches[0]:n).clientY,s=document.elementFromPoint(a,l);if(ot=n,o||this.options.forceAutoScrollFallback||Ue||ce||Me){gt(n,this.options,s,o);var i=ge(s,!0);Et&&(!Fe||a!==pt||l!==mt)&&(Fe&&Ht(),Fe=setInterval(function(){var u=ge(document.elementFromPoint(a,l),!0);u!==i&&(i=u,Ze()),gt(n,r.options,u,o)},10),pt=a,mt=l)}else{if(!this.options.bubbleScroll||ge(s,!0)===oe()){Ze();return}gt(n,this.options,ge(s,!1),!1)}}},de(t,{pluginName:"scroll",initializeByDefault:!0})}function Ze(){N.forEach(function(t){clearInterval(t.pid)}),N=[]}function Ht(){clearInterval(Fe)}var gt=Jt(function(t,e,n,o){if(e.scroll){var r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,s=e.scrollSpeed,i=oe(),u=!1,c;wt!==n&&(wt=n,Ze(),Ne=e.scroll,c=e.scrollFn,Ne===!0&&(Ne=ge(n,!0)));var f=0,w=Ne;do{var S=w,E=F(S),x=E.top,$=E.bottom,Q=E.left,M=E.right,H=E.width,J=E.height,ae=void 0,y=void 0,p=S.scrollWidth,L=S.scrollHeight,U=g(S),ie=S.scrollLeft,te=S.scrollTop;S===i?(ae=H<p&&(U.overflowX==="auto"||U.overflowX==="scroll"||U.overflowX==="visible"),y=J<L&&(U.overflowY==="auto"||U.overflowY==="scroll"||U.overflowY==="visible")):(ae=H<p&&(U.overflowX==="auto"||U.overflowX==="scroll"),y=J<L&&(U.overflowY==="auto"||U.overflowY==="scroll"));var le=ae&&(Math.abs(M-r)<=l&&ie+H<p)-(Math.abs(Q-r)<=l&&!!ie),K=y&&(Math.abs($-a)<=l&&te+J<L)-(Math.abs(x-a)<=l&&!!te);if(!N[f])for(var k=0;k<=f;k++)N[k]||(N[k]={});(N[f].vx!=le||N[f].vy!=K||N[f].el!==S)&&(N[f].el=S,N[f].vx=le,N[f].vy=K,clearInterval(N[f].pid),(le!=0||K!=0)&&(u=!0,N[f].pid=setInterval((function(){o&&this.layer===0&&v.active._onTouchMove(ot);var I=N[this.layer].vy?N[this.layer].vy*s:0,Y=N[this.layer].vx?N[this.layer].vx*s:0;typeof c=="function"&&c.call(v.dragged.parentNode[G],Y,I,t,ot,N[this.layer].el)!=="continue"||Kt(N[this.layer].el,Y,I)}).bind({layer:f}),24))),f++}while(e.bubbleScroll&&w!==i&&(w=ge(w,!1)));Et=u}},30),rn=function(e){var n=e.originalEvent,o=e.putSortable,r=e.dragEl,a=e.activeSortable,l=e.dispatchSortableEvent,s=e.hideGhostForTarget,i=e.unhideGhostForTarget;if(n){var u=o||a;s();var c=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,f=document.elementFromPoint(c.clientX,c.clientY);i(),u&&!u.el.contains(f)&&(l("spill"),this.onSpill({dragEl:r,putSortable:o}))}};function Ct(){}Ct.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var r=ke(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),o&&o.animateAll()},drop:rn};de(Ct,{pluginName:"revertOnSpill"});function It(){}It.prototype={onSpill:function(e){var n=e.dragEl,o=e.putSortable,r=o||this.sortable;r.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),r.animateAll()},drop:rn};de(It,{pluginName:"removeOnSpill"});v.mount(new no);v.mount(It,Ct);function vt(t){t.parentElement!==null&&t.parentElement.removeChild(t)}function Yt(t,e,n){const o=n===0?t.children[0]:t.children[n-1].nextSibling;t.insertBefore(e,o)}function oo(){return typeof window<"u"?window.console:global.console}const ro=oo();function ao(t){const e=Object.create(null);return function(o){return e[o]||(e[o]=t(o))}}const io=/-(\w)/g,lo=ao(t=>t.replace(io,(e,n)=>n.toUpperCase())),an=["Start","Add","Remove","Update","End"],ln=["Choose","Unchoose","Sort","Filter","Clone"],sn=["Move"],so=[sn,an,ln].flatMap(t=>t).map(t=>`on${t}`),Dt={manage:sn,manageAndEmit:an,emit:ln};function uo(t){return so.indexOf(t)!==-1}const co=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","math","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","script","section","select","slot","small","source","span","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr"];function fo(t){return co.includes(t)}function ho(t){return["transition-group","TransitionGroup"].includes(t)}function un(t){return["id","class","role","style"].includes(t)||t.startsWith("data-")||t.startsWith("aria-")||t.startsWith("on")}function dn(t){return t.reduce((e,[n,o])=>(e[n]=o,e),{})}function po({$attrs:t,componentData:e={}}){return{...dn(Object.entries(t).filter(([o,r])=>un(o))),...e}}function mo({$attrs:t,callBackBuilder:e}){const n=dn(cn(t));Object.entries(e).forEach(([r,a])=>{Dt[r].forEach(l=>{n[`on${l}`]=a(l)})});const o=`[data-draggable]${n.draggable||""}`;return{...n,draggable:o}}function cn(t){return Object.entries(t).filter(([e,n])=>!un(e)).map(([e,n])=>[lo(e),n]).filter(([e,n])=>!uo(e))}const Vt=({el:t})=>t,go=(t,e)=>t.__draggable_context=e,qt=t=>t.__draggable_context;class vo{constructor({nodes:{header:e,default:n,footer:o},root:r,realList:a}){this.defaultNodes=n,this.children=[...e,...n,...o],this.externalComponent=r.externalComponent,this.rootTransition=r.transition,this.tag=r.tag,this.realList=a}get _isRootComponent(){return this.externalComponent||this.rootTransition}render(e,n){const{tag:o,children:r,_isRootComponent:a}=this;return e(o,n,a?{default:()=>r}:r)}updated(){const{defaultNodes:e,realList:n}=this;e.forEach((o,r)=>{go(Vt(o),{element:n[r],index:r})})}getUnderlyingVm(e){return qt(e)}getVmIndexFromDomIndex(e,n){const{defaultNodes:o}=this,{length:r}=o,a=n.children,l=a.item(e);if(l===null)return r;const s=qt(l);if(s)return s.index;if(r===0)return 0;const i=Vt(o[0]),u=[...a].findIndex(c=>c===i);return e<u?0:r}}function bo(t,e){const n=t[e];return n?n():[]}function yo({$slots:t,realList:e,getKey:n}){const o=e||[],[r,a]=["header","footer"].map(i=>bo(t,i)),{item:l}=t;if(!l)throw new Error("draggable element must have an item slot");const s=o.flatMap((i,u)=>l({element:i,index:u}).map(c=>(c.key=n(i),c.props={...c.props||{},"data-draggable":!0},c)));if(s.length!==o.length)throw new Error("Item slot must have only one child");return{header:r,footer:a,default:s}}function _o(t){const e=ho(t),n=!fo(t)&&!e;return{transition:e,externalComponent:n,tag:n?B(t):e?vn:t}}function wo({$slots:t,tag:e,realList:n,getKey:o}){const r=yo({$slots:t,realList:n,getKey:o}),a=_o(e);return new vo({nodes:r,root:a,realList:n})}function fn(t,e){$t(()=>this.$emit(t.toLowerCase(),e))}function hn(t){return(e,n)=>{if(this.realList!==null)return this[`onDrag${t}`](e,n)}}function Eo(t){const e=hn.call(this,t);return(n,o)=>{e.call(this,n,o),fn.call(this,t,n)}}let bt=null;const Do={list:{type:Array,required:!1,default:null},modelValue:{type:Array,required:!1,default:null},itemKey:{type:[String,Function],required:!0},clone:{type:Function,default:t=>t},tag:{type:String,default:"div"},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},So=["update:modelValue","change",...[...Dt.manageAndEmit,...Dt.emit].map(t=>t.toLowerCase())],xo=bn({name:"draggable",inheritAttrs:!1,props:Do,emits:So,data(){return{error:!1}},render(){try{this.error=!1;const{$slots:t,$attrs:e,tag:n,componentData:o,realList:r,getKey:a}=this,l=wo({$slots:t,tag:n,realList:r,getKey:a});this.componentStructure=l;const s=po({$attrs:e,componentData:o});return l.render(kt,s)}catch(t){return this.error=!0,kt("pre",{style:{color:"red"}},t.stack)}},created(){this.list!==null&&this.modelValue!==null&&ro.error("modelValue and list props are mutually exclusive! Please set one or another.")},mounted(){if(this.error)return;const{$attrs:t,$el:e,componentStructure:n}=this;n.updated();const o=mo({$attrs:t,callBackBuilder:{manageAndEmit:a=>Eo.call(this,a),emit:a=>fn.bind(this,a),manage:a=>hn.call(this,a)}}),r=e.nodeType===1?e:e.parentElement;this._sortable=new v(r,o),this.targetDomElement=r,r.__draggable_component__=this},updated(){this.componentStructure.updated()},beforeUnmount(){this._sortable!==void 0&&this._sortable.destroy()},computed:{realList(){const{list:t}=this;return t||this.modelValue},getKey(){const{itemKey:t}=this;return typeof t=="function"?t:e=>e[t]}},watch:{$attrs:{handler(t){const{_sortable:e}=this;e&&cn(t).forEach(([n,o])=>{e.option(n,o)})},deep:!0}},methods:{getUnderlyingVm(t){return this.componentStructure.getUnderlyingVm(t)||null},getUnderlyingPotencialDraggableComponent(t){return t.__draggable_component__},emitChanges(t){$t(()=>this.$emit("change",t))},alterList(t){if(this.list){t(this.list);return}const e=[...this.modelValue];t(e),this.$emit("update:modelValue",e)},spliceList(){const t=e=>e.splice(...arguments);this.alterList(t)},updatePosition(t,e){const n=o=>o.splice(e,0,o.splice(t,1)[0]);this.alterList(n)},getRelatedContextFromMoveEvent({to:t,related:e}){const n=this.getUnderlyingPotencialDraggableComponent(t);if(!n)return{component:n};const o=n.realList,r={list:o,component:n};return t!==e&&o?{...n.getUnderlyingVm(e)||{},...r}:r},getVmIndexFromDomIndex(t){return this.componentStructure.getVmIndexFromDomIndex(t,this.targetDomElement)},onDragStart(t){this.context=this.getUnderlyingVm(t.item),t.item._underlying_vm_=this.clone(this.context.element),bt=t.item},onDragAdd(t){const e=t.item._underlying_vm_;if(e===void 0)return;vt(t.item);const n=this.getVmIndexFromDomIndex(t.newIndex);this.spliceList(n,0,e);const o={element:e,newIndex:n};this.emitChanges({added:o})},onDragRemove(t){if(Yt(this.$el,t.item,t.oldIndex),t.pullMode==="clone"){vt(t.clone);return}const{index:e,element:n}=this.context;this.spliceList(e,1);const o={element:n,oldIndex:e};this.emitChanges({removed:o})},onDragUpdate(t){vt(t.item),Yt(t.from,t.item,t.oldIndex);const e=this.context.index,n=this.getVmIndexFromDomIndex(t.newIndex);this.updatePosition(e,n);const o={element:this.context.element,oldIndex:e,newIndex:n};this.emitChanges({moved:o})},computeFutureIndex(t,e){if(!t.element)return 0;const n=[...e.to.children].filter(l=>l.style.display!=="none"),o=n.indexOf(e.related),r=t.component.getVmIndexFromDomIndex(o);return n.indexOf(bt)!==-1||!e.willInsertAfter?r:r+1},onDragMove(t,e){const{move:n,realList:o}=this;if(!n||!o)return!0;const r=this.getRelatedContextFromMoveEvent(t),a=this.computeFutureIndex(r,t),l={...this.context,futureIndex:a},s={...t,relatedContext:r,draggedContext:l};return n(s,e)},onDragEnd(){bt=null}}});const Co=t=>(Sn("data-v-d9a2b35e"),t=t(),xn(),t),Io={class:"json-schema-editor"},Oo={class:"editor-mode-switch"},To={key:0,class:"visual-editor"},ko={class:"properties-list"},Po={class:"properties-header"},Ao=Co(()=>he("h3",null,"参数列表",-1)),No={class:"property-item"},Fo={class:"property-header"},Mo={class:"property-name"},Lo={class:"property-actions"},Ro={key:0,class:"property-description"},jo={key:1,class:"json-editor"},Bo={__name:"JsonSchemaEditor",props:{value:{type:String,required:!0},isHttpTool:{type:Boolean,default:!1}},emits:["update:value"],setup(t,{emit:e}){const n=t,o=be("visual"),r=be(n.value),a=be([]),l=be([]),s=be(!1),i=be({key:"",schema:{type:"string",description:""},required:!1}),u=be(-1),c=Pt(()=>u.value===-1);_n(()=>{f()}),At(()=>n.value,y=>{y!==r.value&&(r.value=y,o.value==="visual"&&f())},{deep:!0});const f=()=>{try{const y=JSON.parse(r.value);if(a.value=[],y.properties)for(const[p,L]of Object.entries(y.properties))a.value.push({key:p,schema:L});l.value=y.required||[]}catch(y){it.error("JSON格式错误: "+y.message)}},w=()=>{const y={type:"object",properties:{},required:[...l.value]};for(const p of a.value)y.properties[p.key]={...p.schema};return y},S=()=>{const y=w();r.value=JSON.stringify(y,null,2),e("update:value",r.value)},E=()=>{e("update:value",r.value),o.value==="visual"&&f()},x=()=>{i.value={key:"",schema:{type:"string",description:"",location:n.isHttpTool?"body":void 0},required:!1},u.value=-1,s.value=!0},$=y=>{const p=a.value[y];i.value={key:p.key,schema:{...p.schema},required:H(p.key)},u.value=y,s.value=!0},Q=y=>{const p=a.value[y].key,L=l.value.indexOf(p);L!==-1&&l.value.splice(L,1),a.value.splice(y,1),S()},M=()=>{if(!i.value.key){it.error("参数名称不能为空");return}if(c.value&&a.value.some(L=>L.key===i.value.key)){it.error("参数名称已存在");return}i.value.schema.type==="array"&&!i.value.schema.items&&(i.value.schema.items={type:"string"}),c.value?a.value.push({key:i.value.key,schema:{...i.value.schema}}):a.value[u.value].schema={...i.value.schema};const y=l.value.indexOf(i.value.key);i.value.required&&y===-1?l.value.push(i.value.key):!i.value.required&&y!==-1&&l.value.splice(y,1),S(),s.value=!1},H=y=>l.value.includes(y),J=y=>({string:"字符串",number:"数字",integer:"整数",boolean:"布尔值",array:"数组",object:"对象"})[y]||y,ae=()=>{S()};return At(o,y=>{y==="visual"&&f()}),Pt(()=>{const y=[{title:"参数名",dataIndex:"name",key:"name",width:"15%"},{title:"类型",dataIndex:"type",key:"type",width:"10%"},{title:"描述",dataIndex:"description",key:"description",width:"20%"},{title:"必填",dataIndex:"required",key:"required",width:"8%"},{title:"默认值",dataIndex:"default",key:"default",width:"15%"},{title:"操作",key:"action",width:"12%"}];return a.value.some(L=>L.schema.location)&&y.splice(1,0,{title:"参数位置",dataIndex:"location",key:"location",width:"10%"}),y}),(y,p)=>{const L=B("a-radio-button"),U=B("a-radio-group"),ie=B("a-button"),te=B("a-empty"),le=B("a-tag"),K=B("a-input"),k=B("a-form-item"),I=B("a-select-option"),Y=B("a-select"),Se=B("a-textarea"),fe=B("a-switch"),ve=B("a-input-number"),Ot=B("a-alert"),He=B("a-form"),at=B("a-modal");return V(),ye("div",Io,[he("div",Oo,[h(U,{value:o.value,"onUpdate:value":p[0]||(p[0]=b=>o.value=b),"button-style":"solid"},{default:m(()=>[h(L,{value:"visual"},{default:m(()=>[O("图形化编辑")]),_:1}),h(L,{value:"json"},{default:m(()=>[O("JSON编辑")]),_:1})]),_:1},8,["value"])]),o.value==="visual"?(V(),ye("div",To,[he("div",ko,[he("div",Po,[Ao,h(ie,{type:"primary",size:"small",onClick:x},{icon:m(()=>[h(Pe(wn))]),default:m(()=>[O(" 添加参数 ")]),_:1})]),a.value.length?(V(),_e(Pe(xo),{key:1,modelValue:a.value,"onUpdate:modelValue":p[1]||(p[1]=b=>a.value=b),"item-key":"key",handle:".drag-handle","ghost-class":"ghost",onEnd:ae},{item:m(({element:b,index:Tt})=>[he("div",No,[he("div",Fo,[h(Pe(Tn),{class:"drag-handle"}),he("span",Mo,Ye(b.key),1),h(le,{color:"blue"},{default:m(()=>[O(Ye(J(b.schema.type)),1)]),_:2},1024),b.schema.location?(V(),_e(le,{key:0,color:"purple"},{default:m(()=>[O(Ye(b.schema.location),1)]),_:2},1024)):se("",!0),H(b.key)?(V(),_e(le,{key:1,color:"red"},{default:m(()=>[O("必填")]),_:1})):se("",!0),he("div",Lo,[h(ie,{type:"text",size:"small",onClick:pn=>$(Tt)},{icon:m(()=>[h(Pe(En))]),_:2},1032,["onClick"]),h(ie,{type:"text",size:"small",onClick:pn=>Q(Tt)},{icon:m(()=>[h(Pe(Dn))]),_:2},1032,["onClick"])])]),b.schema.description?(V(),ye("div",Ro,Ye(b.schema.description),1)):se("",!0)])]),_:1},8,["modelValue"])):(V(),_e(te,{key:0,description:"暂无参数定义"}))])])):(V(),ye("div",jo,[h(mn,{value:r.value,"onUpdate:value":p[2]||(p[2]=b=>r.value=b),language:"json",options:{automaticLayout:!0,scrollBeyondLastLine:!1},onChange:E},null,8,["value"])])),h(at,{open:s.value,"onUpdate:open":p[18]||(p[18]=b=>s.value=b),title:c.value?"添加参数":"编辑参数",onOk:M,onCancel:p[19]||(p[19]=b=>s.value=!1),"ok-text":"确定","cancel-text":"取消"},{default:m(()=>[h(He,{model:i.value,layout:"vertical"},{default:m(()=>[h(k,{label:"参数名称",required:""},{default:m(()=>[h(K,{value:i.value.key,"onUpdate:value":p[3]||(p[3]=b=>i.value.key=b),placeholder:"请输入参数名称",disabled:!c.value},null,8,["value","disabled"])]),_:1}),t.isHttpTool?(V(),_e(k,{key:0,label:"参数位置",required:""},{default:m(()=>[h(Y,{value:i.value.schema.location,"onUpdate:value":p[4]||(p[4]=b=>i.value.schema.location=b),placeholder:"请选择参数位置"},{default:m(()=>[h(I,{value:"url"},{default:m(()=>[O("URL")]),_:1}),h(I,{value:"header"},{default:m(()=>[O("Header")]),_:1}),h(I,{value:"body"},{default:m(()=>[O("Body")]),_:1})]),_:1},8,["value"])]),_:1})):se("",!0),h(k,{label:"参数类型",required:""},{default:m(()=>[h(Y,{value:i.value.schema.type,"onUpdate:value":p[5]||(p[5]=b=>i.value.schema.type=b),placeholder:"请选择参数类型"},{default:m(()=>[h(I,{value:"string"},{default:m(()=>[O("字符串 (string)")]),_:1}),h(I,{value:"number"},{default:m(()=>[O("数字 (number)")]),_:1}),h(I,{value:"integer"},{default:m(()=>[O("整数 (integer)")]),_:1}),h(I,{value:"boolean"},{default:m(()=>[O("布尔值 (boolean)")]),_:1}),h(I,{value:"array"},{default:m(()=>[O("数组 (array)")]),_:1}),h(I,{value:"object"},{default:m(()=>[O("对象 (object)")]),_:1})]),_:1},8,["value"])]),_:1}),h(k,{label:"描述"},{default:m(()=>[h(Se,{value:i.value.schema.description,"onUpdate:value":p[6]||(p[6]=b=>i.value.schema.description=b),placeholder:"请输入参数描述",rows:2},null,8,["value"])]),_:1}),h(k,{label:"是否必填"},{default:m(()=>[h(fe,{checked:i.value.required,"onUpdate:checked":p[7]||(p[7]=b=>i.value.required=b)},null,8,["checked"])]),_:1}),i.value.schema.type==="string"?(V(),ye(lt,{key:1},[h(k,{label:"默认值"},{default:m(()=>[h(K,{value:i.value.schema.default,"onUpdate:value":p[8]||(p[8]=b=>i.value.schema.default=b),placeholder:"请输入默认值"},null,8,["value"])]),_:1}),h(k,{label:"格式"},{default:m(()=>[h(Y,{value:i.value.schema.format,"onUpdate:value":p[9]||(p[9]=b=>i.value.schema.format=b),placeholder:"请选择格式",allowClear:""},{default:m(()=>[h(I,{value:"email"},{default:m(()=>[O("邮箱 (email)")]),_:1}),h(I,{value:"uri"},{default:m(()=>[O("URI (uri)")]),_:1}),h(I,{value:"date"},{default:m(()=>[O("日期 (date)")]),_:1}),h(I,{value:"date-time"},{default:m(()=>[O("日期时间 (date-time)")]),_:1}),h(I,{value:"password"},{default:m(()=>[O("密码 (password)")]),_:1}),h(I,{value:"textarea"},{default:m(()=>[O("文本域 (textarea)")]),_:1})]),_:1},8,["value"])]),_:1}),h(k,{label:"枚举值"},{default:m(()=>[h(Y,{value:i.value.schema.enum,"onUpdate:value":p[10]||(p[10]=b=>i.value.schema.enum=b),mode:"tags",placeholder:"输入后按回车添加枚举值",tokenSeparators:[","]},null,8,["value"])]),_:1})],64)):se("",!0),i.value.schema.type==="number"||i.value.schema.type==="integer"?(V(),ye(lt,{key:2},[h(k,{label:"默认值"},{default:m(()=>[h(ve,{value:i.value.schema.default,"onUpdate:value":p[11]||(p[11]=b=>i.value.schema.default=b),placeholder:"请输入默认值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1}),h(k,{label:"最小值"},{default:m(()=>[h(ve,{value:i.value.schema.minimum,"onUpdate:value":p[12]||(p[12]=b=>i.value.schema.minimum=b),placeholder:"请输入最小值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1}),h(k,{label:"最大值"},{default:m(()=>[h(ve,{value:i.value.schema.maximum,"onUpdate:value":p[13]||(p[13]=b=>i.value.schema.maximum=b),placeholder:"请输入最大值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1})],64)):se("",!0),i.value.schema.type==="boolean"?(V(),_e(k,{key:3,label:"默认值"},{default:m(()=>[h(fe,{checked:i.value.schema.default,"onUpdate:checked":p[14]||(p[14]=b=>i.value.schema.default=b)},null,8,["checked"])]),_:1})):se("",!0),i.value.schema.type==="array"?(V(),ye(lt,{key:4},[h(k,{label:"数组元素类型",required:""},{default:m(()=>[h(Y,{value:i.value.schema.items.type,"onUpdate:value":p[15]||(p[15]=b=>i.value.schema.items.type=b),placeholder:"请选择数组元素类型"},{default:m(()=>[h(I,{value:"string"},{default:m(()=>[O("字符串 (string)")]),_:1}),h(I,{value:"number"},{default:m(()=>[O("数字 (number)")]),_:1}),h(I,{value:"integer"},{default:m(()=>[O("整数 (integer)")]),_:1}),h(I,{value:"boolean"},{default:m(()=>[O("布尔值 (boolean)")]),_:1}),h(I,{value:"object"},{default:m(()=>[O("对象 (object)")]),_:1})]),_:1},8,["value"])]),_:1}),h(k,{label:"最小元素数量"},{default:m(()=>[h(ve,{value:i.value.schema.minItems,"onUpdate:value":p[16]||(p[16]=b=>i.value.schema.minItems=b),placeholder:"请输入最小元素数量",min:0,precision:0,style:{width:"100%"}},null,8,["value"])]),_:1}),h(k,{label:"最大元素数量"},{default:m(()=>[h(ve,{value:i.value.schema.maxItems,"onUpdate:value":p[17]||(p[17]=b=>i.value.schema.maxItems=b),placeholder:"请输入最大元素数量",min:0,precision:0,style:{width:"100%"}},null,8,["value"])]),_:1})],64)):se("",!0),i.value.schema.type==="object"?(V(),_e(Ot,{key:5,message:"对象类型提示",description:"对象类型参数可以包含嵌套属性，保存后可以在图形化编辑器中单独编辑其属性。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}})):se("",!0)]),_:1},8,["model"])]),_:1},8,["open","title"])])}}},Ho=yn(Bo,[["__scopeId","data-v-d9a2b35e"]]);export{Ho as J};
