import{A as R,c as z}from"./AppLayout-94293e8c.js";import{_ as U,r as l,B as $,o as D,b as c,c as s,d as h,w as i,e as t,h as e,f as L,R as S,g as b,O as B,k as _,l as Q,F,v as T,t as d,Q as j,p as q,m as G}from"./index-01e50f26.js";const H=r=>(q("data-v-abb00f56"),r=r(),G(),r),J={class:"log-container"},K={class:"log-files-panel"},P={class:"panel-header"},W=H(()=>e("h3",null,"日志文件",-1)),X=["onClick"],Y={class:"log-file-info"},Z={class:"log-file-name"},ee={class:"log-file-meta"},ae={class:"log-content-panel"},se={key:0,class:"no-log-selected"},te={key:1,class:"log-content-container"},oe={class:"panel-header"},ne={class:"log-controls"},le={class:"log-info-bar"},ce={class:"log-content-viewer"},ie={key:1},de={__name:"LogListView",setup(r){const u=l(!1),f=l(!1),y=l([]),o=l(null),p=l({content:"",total_lines:0,displayed_lines:0}),v=l(1e3),m=l(!0),w=async()=>{u.value=!0;try{await z({method:"get",url:"/api/v1/log",onSuccess:n=>{y.value=n.files},errorMessage:"获取日志文件列表失败"})}finally{u.value=!1}},I=()=>{w()},M=n=>{o.value=n,k()},k=async()=>{if(o.value){f.value=!0;try{await z({method:"get",url:`/api/v1/log/content/${o.value.name}`,params:{max_lines:v.value,tail:m.value},onSuccess:n=>{p.value=n},errorMessage:"获取日志内容失败"})}catch(n){console.error("Error loading log content:",n)}finally{f.value=!1}}};return $([v,m],()=>{o.value&&k()}),D(()=>{w()}),(n,g)=>{const C=c("a-button"),x=c("a-spin"),V=c("a-empty"),N=c("a-input-number"),A=c("a-switch"),O=c("a-space"),E=c("a-card");return s(),h(R,{"current-page-key":"log"},{default:i(()=>[t(E,{title:"系统日志"},{default:i(()=>[e("div",J,[e("div",K,[e("div",P,[W,t(C,{type:"primary",size:"small",onClick:I},{icon:i(()=>[t(L(S))]),default:i(()=>[b(" 刷新 ")]),_:1})]),e("div",{class:B(["log-files-list",{loading:u.value}])},[u.value?(s(),h(x,{key:0})):(s(),_(F,{key:1},[y.value.length===0?(s(),h(V,{key:0,description:"没有找到日志文件"})):Q("",!0),(s(!0),_(F,null,T(y.value,a=>(s(),_("div",{key:a.name,class:B(["log-file-card",{active:o.value&&o.value.name===a.name}]),onClick:_e=>M(a)},[e("div",Y,[e("div",Z,[t(L(j)),b(" "+d(a.name),1)]),e("div",ee,[e("div",null,"大小: "+d(a.size_human),1),e("div",null,"修改时间: "+d(a.modified_at_human),1)])])],10,X))),128))],64))],2)]),e("div",ae,[o.value?(s(),_("div",te,[e("div",oe,[e("h3",null,d(o.value.name),1),e("div",ne,[t(O,null,{default:i(()=>[t(N,{value:v.value,"onUpdate:value":g[0]||(g[0]=a=>v.value=a),min:100,max:1e4,step:100,size:"small","addon-before":"行数"},null,8,["value"]),t(A,{checked:m.value,"onUpdate:checked":g[1]||(g[1]=a=>m.value=a),"checked-children":"末尾","un-checked-children":"开头",size:"small"},null,8,["checked"]),t(C,{type:"primary",size:"small",onClick:k},{icon:i(()=>[t(L(S))]),default:i(()=>[b(" 刷新 ")]),_:1})]),_:1})])]),e("div",le,[e("span",null,"总行数: "+d(p.value.total_lines),1),e("span",null,"显示行数: "+d(p.value.displayed_lines),1)]),e("div",ce,[f.value?(s(),h(x,{key:0})):(s(),_("pre",ie,d(p.value.content),1))])])):(s(),_("div",se,[t(V,{description:"请选择一个日志文件查看"})]))])])]),_:1})]),_:1})}}},pe=U(de,[["__scopeId","data-v-abb00f56"]]);export{pe as default};
