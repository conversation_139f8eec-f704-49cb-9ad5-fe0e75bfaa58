import{r as f,o as C,n as _,I as k,B as r,c as p,k as g,e as z,f as B,S as L,l as b,h as E,J as M,K as P,L as v}from"./index-01e50f26.js";/* empty css                                                     */const S={class:"monaco-editor-wrapper"},x={key:0,class:"monaco-editor-loading"},I={__name:"MonacoEditor",props:{value:{type:String,default:""},language:{type:String,default:"javascript"},options:{type:Object,default:()=>({})},height:{type:String,default:"400px"},readOnly:{type:Boolean,default:!1}},emits:["update:value","editor-mounted"],setup(l,{expose:y,emit:u}){const o=l,s=f(null),d=f(!0);let e=null,a=null,i=null;const m=async()=>{!s.value||e||(P(),e=v.create(s.value,{value:o.value,language:o.language,theme:"vs",automaticLayout:!1,scrollBeyondLastLine:!1,minimap:{enabled:!1},readOnly:o.readOnly,...o.options}),i=e.onDidChangeModelContent(()=>{const t=e.getValue();u("update:value",t)}),window.ResizeObserver?(a=new ResizeObserver(()=>{e&&e.layout()}),a.observe(s.value)):window.addEventListener("resize",c),u("editor-mounted",e),d.value=!1)},c=()=>{e&&e.layout()},h=async()=>{if(!e)return!1;try{return await e.getAction("editor.action.formatDocument").run(),!0}catch(t){return console.error("格式化代码时出错:",t),!1}},O=(t,n=1)=>{e&&(e.setPosition({lineNumber:t,column:n}),e.revealPositionInCenter({lineNumber:t,column:n}),e.focus())},w=()=>e;return C(async()=>{await _(),m()}),k(()=>{window.removeEventListener("resize",c),a&&(a.disconnect(),a=null),i&&(i.dispose(),i=null),e&&(e.dispose(),e=null)}),r(()=>o.value,t=>{if(e&&t!==e.getValue()){const n=e.getPosition();e.setValue(t),n&&(e.setPosition(n),e.revealPositionInCenter(n))}}),r(()=>o.language,t=>{e&&v.setModelLanguage(e.getModel(),t)}),r(()=>o.options,t=>{e&&e.updateOptions(t)},{deep:!0}),r(()=>o.readOnly,t=>{e&&e.updateOptions({readOnly:t})}),y({formatCode:h,setCursorPosition:O,getEditor:w}),(t,n)=>(p(),g("div",S,[d.value?(p(),g("div",x,[z(B(L),{tip:"加载编辑器中..."})])):b("",!0),E("div",{ref_key:"editorContainer",ref:s,class:"monaco-editor-container",style:M({height:l.height})},null,4)]))}};export{I as _};
