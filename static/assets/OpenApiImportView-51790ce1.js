import{e,A as Z,_ as K,u as ee,r as v,b as s,c as u,d as O,w as o,f as P,g as _,k as h,h as f,t as y,l as b,F as te,v as oe,q as S,p as ae,m as ne}from"./index-01e50f26.js";import{A as se,c as L}from"./AppLayout-94293e8c.js";import{R as le}from"./RollbackOutlined-f857b260.js";var re={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"};const ie=re;function N(i){for(var c=1;c<arguments.length;c++){var a=arguments[c]!=null?Object(arguments[c]):{},p=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(p=p.concat(Object.getOwnPropertySymbols(a).filter(function(d){return Object.getOwnPropertyDescriptor(a,d).enumerable}))),p.forEach(function(d){ce(i,d,a[d])})}return i}function ce(i,c,a){return c in i?Object.defineProperty(i,c,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[c]=a,i}var C=function(c,a){var p=N({},c,a.attrs);return e(Z,N({},p,{icon:ie}),null)};C.displayName="InboxOutlined";C.inheritAttrs=!1;const pe=C;const R=i=>(ae("data-v-14ab1ab9"),i=i(),ne(),i),ue={key:0,class:"step-content"},de={class:"ant-upload-drag-icon"},_e=R(()=>f("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),me=R(()=>f("p",{class:"ant-upload-hint"}," 仅支持 JSON 格式的 OpenAPI (Swagger) 文件 ",-1)),ve={key:0,class:"error-message"},fe={key:1,class:"step-content"},ge={key:0,class:"empty-state"},he={class:"step-actions"},ye={key:2,class:"step-content"},be={class:"selected-apis"},Ie={class:"step-actions"},Ae={__name:"OpenApiImportView",setup(i){const c=ee(),a=v(0),p=v(""),d=v(!1),w=v(!1),I=v([]),m=v([]),g=v({baseUrl:""}),j=[{title:"方法",key:"method",width:100},{title:"路径",dataIndex:"path",key:"path"},{title:"工具名称",dataIndex:"tool",key:"tool"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0}],z=l=>({get:"blue",post:"green",put:"orange",delete:"red",patch:"purple"})[l.toLowerCase()]||"default",A=l=>I.value.find(t=>t.tool===l)||{},B=l=>{const t=l.type==="application/json";return t||S.error("只能上传 JSON 文件!"),t||Upload.LIST_IGNORE},$=async({file:l})=>{p.value="",d.value=!0;const t=new FormData;t.append("file",l);try{await L({method:"post",url:"/api/v1/tool-openapi/analyze",data:t,headers:{"Content-Type":"multipart/form-data"},onSuccess:n=>{I.value=n.apis||[],n.server&&(g.value.baseUrl=n.server),a.value=1},errorMessage:"分析 OpenAPI 文件失败"})}catch(n){p.value=n.message||"上传文件失败"}finally{d.value=!1}},F=l=>{m.value=l},M=async()=>{if(m.value.length===0){S.warning("请选择要导入的 API 接口");return}if(!g.value.baseUrl){S.warning("请输入 API 基础 URL");return}w.value=!0;try{const l=m.value.map(t=>{const n=A(t);return{path:n.path,method:n.method,tool:n.tool,description:n.description,parameters:n.parameters}});await L({method:"post",url:"/api/v1/tool-openapi/import",data:{server:g.value.baseUrl,apis:l},successMessage:"工具生成成功",onSuccess:()=>{c.push("/tool")},errorMessage:"生成工具失败"})}catch(l){console.error("Failed to generate tools:",l)}finally{w.value=!1}};return(l,t)=>{const n=s("a-button"),k=s("a-step"),q=s("a-steps"),x=s("a-alert"),D=s("a-upload-dragger"),E=s("a-empty"),U=s("a-tag"),H=s("a-table"),J=s("a-spin"),T=s("a-input"),G=s("a-form-item"),Q=s("a-form"),W=s("a-list-item"),X=s("a-list"),Y=s("a-card");return u(),O(se,{"current-page-key":"tool"},{default:o(()=>[e(Y,{title:"导入 OpenAPI (Swagger)"},{extra:o(()=>[e(n,{onClick:t[0]||(t[0]=r=>P(c).push("/tool"))},{icon:o(()=>[e(P(le))]),default:o(()=>[_(" 返回 ")]),_:1})]),default:o(()=>[e(q,{current:a.value,size:"small",style:{"margin-bottom":"24px"}},{default:o(()=>[e(k,{title:"上传 API 文件"}),e(k,{title:"选择 API 接口"}),e(k,{title:"生成工具"})]),_:1},8,["current"]),a.value===0?(u(),h("div",ue,[e(x,{message:"上传 OpenAPI (Swagger) 文件",description:"请上传 OpenAPI (Swagger) JSON 文件，系统将自动分析文件内容并提取 API 接口信息。支持 OpenAPI v2 和 v3 格式。",type:"info","show-icon":"",style:{"margin-bottom":"24px"}}),e(D,{name:"file",multiple:!1,"before-upload":B,"custom-request":$,"show-upload-list":!1,accept:"application/json"},{default:o(()=>[f("p",de,[e(P(pe))]),_e,me]),_:1}),p.value?(u(),h("div",ve,y(p.value),1)):b("",!0)])):b("",!0),a.value===1?(u(),h("div",fe,[e(x,{message:"选择要导入的 API 接口",description:"请选择要导入的 API 接口，系统将根据选择的接口生成工具。",type:"info","show-icon":"",style:{"margin-bottom":"24px"}}),e(J,{spinning:d.value},{default:o(()=>[I.value.length===0&&!d.value?(u(),h("div",ge,[e(E,{description:"没有可用的 API 接口"})])):(u(),O(H,{key:1,columns:j,"data-source":I.value,pagination:!1,"row-selection":{selectedRowKeys:m.value,onChange:F,type:"checkbox"},"row-key":r=>r.tool},{bodyCell:o(({column:r,record:V})=>[r.key==="method"?(u(),O(U,{key:0,color:z(V.method)},{default:o(()=>[_(y(V.method.toUpperCase()),1)]),_:2},1032,["color"])):b("",!0)]),_:1},8,["data-source","row-selection","row-key"]))]),_:1},8,["spinning"]),f("div",he,[e(n,{onClick:t[1]||(t[1]=r=>a.value=0)},{default:o(()=>[_("上一步")]),_:1}),e(n,{type:"primary",disabled:m.value.length===0,onClick:t[2]||(t[2]=r=>a.value=2)},{default:o(()=>[_("下一步")]),_:1},8,["disabled"])])])):b("",!0),a.value===2?(u(),h("div",ye,[e(x,{message:"生成工具",description:"系统将根据选择的 API 接口生成工具，请确认以下信息。",type:"info","show-icon":"",style:{"margin-bottom":"24px"}}),e(Q,{model:g.value,layout:"vertical"},{default:o(()=>[e(G,{label:"API 基础 URL",required:""},{default:o(()=>[e(T,{value:g.value.baseUrl,"onUpdate:value":t[3]||(t[3]=r=>g.value.baseUrl=r),placeholder:"请输入 API 基础 URL，例如: https://api.example.com"},null,8,["value"])]),_:1})]),_:1},8,["model"]),f("div",be,[f("h3",null,"已选择的 API 接口 ("+y(m.value.length)+")",1),e(X,{size:"small",bordered:""},{default:o(()=>[(u(!0),h(te,null,oe(m.value,r=>(u(),O(W,{key:r},{actions:o(()=>[e(U,null,{default:o(()=>[_(y(A(r).tool),1)]),_:2},1024)]),default:o(()=>[_(y(A(r).method.toUpperCase())+" "+y(A(r).path)+" ",1)]),_:2},1024))),128))]),_:1})]),f("div",Ie,[e(n,{onClick:t[4]||(t[4]=r=>a.value=1)},{default:o(()=>[_("上一步")]),_:1}),e(n,{type:"primary",loading:w.value,onClick:M},{default:o(()=>[_("生成工具")]),_:1},8,["loading"])])])):b("",!0)]),_:1})]),_:1})}}},xe=K(Ae,[["__scopeId","data-v-14ab1ab9"]]);export{xe as default};
