import{c as x,A as Q,f as W}from"./AppLayout-94293e8c.js";import{_ as X,r as i,a as Y,o as ee,b as l,c as d,d as g,w as n,e as o,h as ae,f as v,P as te,g as f,t as C,k as V,E as ne,x as oe,l as le}from"./index-01e50f26.js";const se={class:"action-bar"},ie={key:1},re={key:3},ce={__name:"TagListView",setup(ue){const y=i(!1),S=i([]),z=i(0),p=i(1),m=i(10),h=i(""),_=i(!1),k=i(!1),r=i(null),w=i(),s=Y({name:"",description:""}),$={name:[{required:!0,message:"请输入标签名称",trigger:"blur"},{min:1,max:50,message:"标签名称长度应在1-50个字符之间",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"标签名称只能包含字母和数字",trigger:"blur"}],description:[{max:500,message:"描述长度不能超过500个字符",trigger:"blur"}]},E=[{title:"标签名称",key:"name",width:150},{title:"描述",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"关联工具数",key:"tool_count",width:120,align:"center"},{title:"创建时间",key:"created_at",width:160},{title:"操作",key:"action",width:150,fixed:"right"}];ee(()=>{c()});const c=async()=>{y.value=!0;try{await x({method:"get",url:"/api/v1/tag/with-count",params:{page:p.value,size:m.value,search:h.value||void 0},onSuccess:(a,e)=>{S.value=a,z.value=e.total},errorMessage:"获取标签列表失败"})}finally{y.value=!1}},O=()=>{p.value=1,c()},P=(a,e)=>{p.value=a,m.value=e,c()},A=(a,e)=>{p.value=1,m.value=e,c()},B=a=>{r.value=a,s.name=a.name,s.description=a.description||"",_.value=!0},T=()=>{var a;_.value=!1,r.value=null,s.name="",s.description="",(a=w.value)==null||a.resetFields()},D=async()=>{try{await w.value.validate(),k.value=!0;const a=r.value?`/api/v1/tag/${r.value.id}`:"/api/v1/tag",e=r.value?"put":"post";await x({method:e,url:a,data:{name:s.name,description:s.description||null},successMessage:r.value?"更新成功":"创建成功",errorMessage:r.value?"更新失败":"创建失败"}),T(),c()}catch(a){console.error("Error submitting tag:",a)}finally{k.value=!1}},L=async a=>{try{await x({method:"delete",url:`/api/v1/tag/${a}`,successMessage:"删除成功",errorMessage:"删除失败"}),c()}catch(e){console.error("Error deleting tag:",e)}};return(a,e)=>{const N=l("a-input-search"),b=l("a-button"),U=l("a-tag"),R=l("a-badge"),q=l("a-popconfirm"),F=l("a-space"),I=l("a-table"),Z=l("a-card"),j=l("a-input"),M=l("a-form-item"),G=l("a-textarea"),H=l("a-form"),J=l("a-modal");return d(),g(Q,{"current-page-key":"tag"},{default:n(()=>[o(Z,{title:"标签管理"},{default:n(()=>[ae("div",se,[o(N,{value:h.value,"onUpdate:value":e[0]||(e[0]=t=>h.value=t),placeholder:"搜索标签名称或描述",style:{width:"300px"},onSearch:O},null,8,["value"]),o(b,{type:"primary",onClick:e[1]||(e[1]=t=>_.value=!0)},{icon:n(()=>[o(v(te))]),default:n(()=>[f(" 创建标签 ")]),_:1})]),o(I,{columns:E,"data-source":S.value,loading:y.value,pagination:{current:p.value,pageSize:m.value,total:z.value,onChange:P,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:A,showTotal:t=>`共 ${t} 条记录`},"row-key":t=>t.id},{bodyCell:n(({column:t,record:u})=>[t.key==="name"?(d(),g(U,{key:0,style:{margin:"0"}},{default:n(()=>[f(C(u.name),1)]),_:2},1024)):t.key==="description"?(d(),V("span",ie,C(u.description||"无描述"),1)):t.key==="tool_count"?(d(),g(R,{key:2,count:u.tool_count,"number-style":{backgroundColor:"#52c41a"}},null,8,["count"])):t.key==="created_at"?(d(),V("span",re,C(v(W)(u.created_at)),1)):t.key==="action"?(d(),g(F,{key:4,size:0},{default:n(()=>[o(b,{type:"link",size:"small",onClick:K=>B(u)},{icon:n(()=>[o(v(ne))]),default:n(()=>[f(" 编辑 ")]),_:2},1032,["onClick"]),o(q,{title:"确定要删除此标签吗？","ok-text":"确定","cancel-text":"取消",onConfirm:K=>L(u.id)},{default:n(()=>[o(b,{type:"link",size:"small",danger:""},{icon:n(()=>[o(v(oe))]),default:n(()=>[f(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):le("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1}),o(J,{open:_.value,"onUpdate:open":e[4]||(e[4]=t=>_.value=t),title:r.value?"编辑标签":"创建标签",onOk:D,onCancel:T,"confirm-loading":k.value,"ok-text":"确定","cancel-text":"取消"},{default:n(()=>[o(H,{ref_key:"formRef",ref:w,model:s,rules:$,layout:"vertical"},{default:n(()=>[o(M,{label:"标签名称",name:"name"},{default:n(()=>[o(j,{value:s.name,"onUpdate:value":e[2]||(e[2]=t=>s.name=t),placeholder:"请输入标签名称"},null,8,["value"])]),_:1}),o(M,{label:"标签描述",name:"description"},{default:n(()=>[o(G,{value:s.description,"onUpdate:value":e[3]||(e[3]=t=>s.description=t),placeholder:"请输入标签描述（可选）",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","title","confirm-loading"])]),_:1})}}},_e=X(ce,[["__scopeId","data-v-9cf3fe31"]]);export{_e as default};
