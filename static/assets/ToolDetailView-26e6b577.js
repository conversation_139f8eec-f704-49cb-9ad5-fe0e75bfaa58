import{e as n,A as De,_ as ze,u as Be,y as Ee,z as le,r as u,o as Ie,b as h,c as o,d as p,w as s,h as e,t as a,g as y,l as _,f,E as Ne,k as r,v as V,F as $,p as Re,m as Le,q as ne}from"./index-01e50f26.js";import{c as C,A as Ue,a as qe,f as Q,S as Ae,F as Fe}from"./AppLayout-94293e8c.js";/* empty css                                                     */import{B as je,T as He}from"./ToolTagManager-e263c0c8.js";import{P as Ke}from"./PlayCircleOutlined-35a6a3f0.js";import{R as ie}from"./RollbackOutlined-f857b260.js";var Je={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"};const Qe=Je;function ce(g){for(var m=1;m<arguments.length;m++){var b=arguments[m]!=null?Object(arguments[m]):{},v=Object.keys(b);typeof Object.getOwnPropertySymbols=="function"&&(v=v.concat(Object.getOwnPropertySymbols(b).filter(function(x){return Object.getOwnPropertyDescriptor(b,x).enumerable}))),v.forEach(function(x){Ge(g,x,b[x])})}return g}function Ge(g,m,b){return m in g?Object.defineProperty(g,m,{value:b,enumerable:!0,configurable:!0,writable:!0}):g[m]=b,g}var G=function(m,b){var v=ce({},m,b.attrs);return n(De,ce({},v,{icon:Qe}),null)};G.displayName="PauseCircleOutlined";G.inheritAttrs=!1;const We=G;const d=g=>(Re("data-v-17663161"),g=g(),Le(),g),Xe={class:"info-container"},Ye={class:"info-row"},Ze=d(()=>e("div",{class:"info-label"},"工具名称:",-1)),et={class:"info-value"},tt={class:"info-row"},st=d(()=>e("div",{class:"info-label"},"工具描述:",-1)),ot={class:"info-value"},at={class:"info-row"},lt=d(()=>e("div",{class:"info-label"},"工具类型:",-1)),nt={class:"info-value"},it={class:"info-row"},ct=d(()=>e("div",{class:"info-label"},"工具标签:",-1)),rt={class:"info-value"},dt={style:{display:"flex","align-items":"center",gap:"8px","flex-wrap":"wrap"}},ut={key:0,style:{color:"#999"}},_t={class:"info-row"},vt=d(()=>e("div",{class:"info-label"},"状态:",-1)),pt={class:"info-value"},ft={class:"info-row"},mt=d(()=>e("div",{class:"info-label"},"当前版本:",-1)),ht={class:"info-value"},yt={class:"info-row"},gt=d(()=>e("div",{class:"info-label"},"创建时间:",-1)),bt={class:"info-value"},kt={class:"info-row"},wt=d(()=>e("div",{class:"info-label"},"更新时间:",-1)),Ct={class:"info-value"},$t={class:"info-row"},xt=d(()=>e("div",{class:"info-label"},"创建人:",-1)),Ot={class:"info-value"},St={class:"info-row"},Tt=d(()=>e("div",{class:"info-label"},"更新人:",-1)),Mt={class:"info-value"},Pt={class:"info-row"},Vt=d(()=>e("div",{class:"info-label"},"请求地址:",-1)),Dt={class:"info-value"},zt={class:"info-row"},Bt=d(()=>e("div",{class:"info-label"},"请求方法:",-1)),Et={class:"info-value"},It={key:0,class:"info-row"},Nt=d(()=>e("div",{class:"info-label"},"请求头:",-1)),Rt={class:"info-value"},Lt={key:0},Ut={class:"info-row"},qt=d(()=>e("div",{class:"info-label"},"DB URL:",-1)),At={class:"info-value"},Ft={class:"info-row"},jt=d(()=>e("div",{class:"info-label"},"查询SQL:",-1)),Ht={class:"info-value"},Kt={class:"sql-display"},Jt={key:1,class:"parameters-container"},Qt={class:"parameter-header"},Gt={class:"parameter-name"},Wt={key:0,class:"parameter-description"},Xt={key:1,class:"parameter-details"},Yt={key:0,class:"parameter-enum"},Zt=d(()=>e("span",{class:"parameter-detail-label"},"可选值:",-1)),es={class:"parameter-enum-values"},ts={key:1,class:"parameter-format"},ss=d(()=>e("span",{class:"parameter-detail-label"},"格式:",-1)),os={class:"parameter-detail-value"},as={key:2,class:"parameter-items"},ls=d(()=>e("span",{class:"parameter-detail-label"},"元素类型:",-1)),ns={class:"parameter-detail-value"},is={key:3,class:"parameter-default"},cs=d(()=>e("span",{class:"parameter-detail-label"},"默认值:",-1)),rs={class:"parameter-detail-value"},ds={key:4,class:"parameter-min"},us=d(()=>e("span",{class:"parameter-detail-label"},"最小值:",-1)),_s={class:"parameter-detail-value"},vs={key:5,class:"parameter-max"},ps=d(()=>e("span",{class:"parameter-detail-label"},"最大值:",-1)),fs={class:"parameter-detail-value"},ms={key:6,class:"parameter-min-length"},hs=d(()=>e("span",{class:"parameter-detail-label"},"最小长度:",-1)),ys={class:"parameter-detail-value"},gs={key:7,class:"parameter-max-length"},bs=d(()=>e("span",{class:"parameter-detail-label"},"最大长度:",-1)),ks={class:"parameter-detail-value"},ws={class:"code-container"},Cs={class:"code-display"},$s={key:1,class:"version-list"},xs={class:"version-row"},Os=d(()=>e("div",{class:"version-label"},"版本:",-1)),Ss=d(()=>e("div",{class:"version-label time-label"},"发布时间:",-1)),Ts={class:"version-time"},Ms={key:0,class:"version-description"},Ps={class:"code-display"},Vs={class:"rollback-warning"},Ds={class:"rollback-code-container"},zs={class:"rollback-code-header"},Bs={class:"code-display"},Es=["onClick"],Is={class:"config-description"},Ns=["onClick"],Rs={class:"func-description"},Ls={__name:"ToolDetailView",setup(g){const m=Be(),b=Ee(),v=le(()=>b.params.id),x=u(!1),c=u(null),L=u([]),W=u(!1),re=u(1),de=u(10),ue=u(0),X=u("basic"),U=u([]),q=u([]),E=u(!1),D=u(!1),O=u(null),M=u(""),_e=u(null),A=u(!1),F=u(!1),j=u(!1),H=u(""),K=u(!1),ve=[{title:"Key",dataIndex:"key",key:"key",width:"40%"},{title:"Value",dataIndex:"value",key:"value",width:"60%"}],S=le(()=>!c.value||!c.value.parameters?null:c.value.parameters),pe=i=>!S.value||!S.value.required?!1:S.value.required.includes(i);Ie(()=>{z(),Y(),fe(),me()});const z=async()=>{x.value=!0;try{await C({method:"get",url:`/api/v1/tool/${v.value}`,onSuccess:i=>{c.value=i},errorMessage:"获取工具数据失败"})}finally{x.value=!1}},Y=async()=>{W.value=!0;try{await C({method:"get",url:`/api/v1/tool/${v.value}/deploy/history`,params:{page:re.value,size:de.value},onSuccess:(i,l)=>{L.value=i,ue.value=l.total},errorMessage:"获取发布历史失败"})}finally{W.value=!1}},fe=async()=>{try{await C({method:"get",url:`/api/v1/tool/${v.value}/config`,onSuccess:i=>{U.value=i||[]},errorMessage:"获取绑定配置失败"})}catch(i){console.error("Error fetching bound configs:",i)}},me=async()=>{try{await C({method:"get",url:`/api/v1/tool/${v.value}/func`,onSuccess:i=>{q.value=i||[]},errorMessage:"获取依赖函数失败"})}catch(i){console.error("Error fetching dependent functions:",i)}},he=async i=>{try{if(O.value=i.version,i.code){M.value=i.code,E.value=!0;return}await C({method:"get",url:`/api/v1/tool/${v.value}/version/${i.version}`,onSuccess:l=>{M.value=l.code||"// No code available for this version",E.value=!0},errorMessage:"获取工具代码失败"})}catch(l){console.error("Error fetching version code:",l),ne.error("获取工具代码失败")}},ye=async i=>{try{if(O.value=i.version,_e.value=i,i.code){M.value=i.code,D.value=!0;return}await C({method:"get",url:`/api/v1/tool/${v.value}/version/${i.version}`,onSuccess:l=>{M.value=l.code||"// No code available for this version",D.value=!0},errorMessage:"获取工具代码失败"})}catch(l){console.error("Error fetching version code:",l),ne.error("获取工具代码失败")}},ge=async()=>{if(O.value){A.value=!0;try{await C({method:"post",url:`/api/v1/tool/${v.value}/deploy/rollback/${O.value}`,successMessage:"回滚成功",errorMessage:"回滚失败",onSuccess:()=>{z(),D.value=!1}})}catch(i){console.error("Error rolling back tool:",i)}finally{A.value=!1}}},be=async()=>{K.value=!0;try{await C({method:"post",url:`/api/v1/tool/${v.value}/deploy`,data:{description:H.value},successMessage:"发布成功",errorMessage:"发布失败",onSuccess:()=>{j.value=!1,z(),Y()}})}finally{K.value=!1}},Z=async i=>{try{const l=i?"enable":"disable";await C({method:"patch",url:`/api/v1/tool/${v.value}/${l}`,successMessage:`${i?"启用":"禁用"}成功`,errorMessage:`${i?"启用":"禁用"}失败`,onSuccess:()=>{z()}})}catch(l){console.error(`Error ${i?"enabling":"disabling"} tool:`,l)}},ke=i=>{switch(i){case"http":return"blue";case"database":return"purple";default:return"green"}},we=i=>{switch(i){case"http":return"HTTP工具";case"database":return"数据库工具";default:return"基础工具"}};return(i,l)=>{const w=h("a-tag"),T=h("a-button"),Ce=h("a-space"),$e=h("a-table"),P=h("a-tab-pane"),I=h("a-empty"),J=h("a-modal"),xe=h("a-alert"),ee=h("a-list-item-meta"),te=h("a-list-item"),se=h("a-list"),Oe=h("a-tabs"),Se=h("a-card"),Te=h("a-textarea"),Me=h("a-form-item"),Pe=h("a-form");return o(),p(Ue,{"current-page-key":"tool"},{default:s(()=>[c.value?(o(),p(Se,{key:0},{title:s(()=>[e("span",null,"工具详情 - "+a(c.value.name),1),c.value.current_version?(o(),p(w,{key:0,color:"blue",class:"version-tag"},{default:s(()=>[y("v"+a(c.value.current_version),1)]),_:1})):_("",!0)]),extra:s(()=>[n(Ce,null,{default:s(()=>[n(T,{onClick:l[0]||(l[0]=t=>f(m).push("/tool"))},{icon:s(()=>[n(f(ie))]),default:s(()=>[y(" 返回 ")]),_:1}),n(T,{type:"primary",onClick:l[1]||(l[1]=t=>f(m).push(`/tool/${v.value}/edit`))},{icon:s(()=>[n(f(Ne))]),default:s(()=>[y(" 编辑 ")]),_:1}),n(T,{type:"primary",onClick:l[2]||(l[2]=t=>f(m).push(`/tool/${v.value}/debug`))},{icon:s(()=>[n(f(je))]),default:s(()=>[y(" 调试 ")]),_:1}),c.value.is_enabled?(o(),p(T,{key:0,type:"primary",danger:"",onClick:l[3]||(l[3]=t=>Z(!1))},{icon:s(()=>[n(f(We))]),default:s(()=>[y(" 禁用 ")]),_:1})):(o(),p(T,{key:1,type:"primary",onClick:l[4]||(l[4]=t=>Z(!0))},{icon:s(()=>[n(f(Ke))]),default:s(()=>[y(" 启用 ")]),_:1}))]),_:1})]),default:s(()=>[n(Oe,{activeKey:X.value,"onUpdate:activeKey":l[8]||(l[8]=t=>X.value=t)},{default:s(()=>[n(P,{key:"basic",tab:"基本信息"},{default:s(()=>{var t,k,N,R,oe,ae;return[e("div",Xe,[e("div",Ye,[Ze,e("div",et,a(c.value.name),1)]),e("div",tt,[st,e("div",ot,a(c.value.description||"无描述"),1)]),e("div",at,[lt,e("div",nt,[n(w,{color:ke(c.value.type)},{default:s(()=>[y(a(we(c.value.type)),1)]),_:1},8,["color"])])]),e("div",it,[ct,e("div",rt,[e("div",dt,[(o(!0),r($,null,V(c.value.tags,B=>(o(),p(w,{key:B.id,style:{margin:"0"}},{default:s(()=>[y(a(B.name),1)]),_:2},1024))),128)),!c.value.tags||c.value.tags.length===0?(o(),r("span",ut," 无标签 ")):_("",!0),n(T,{type:"link",size:"small",onClick:l[5]||(l[5]=B=>F.value=!0)},{icon:s(()=>[n(f(qe))]),default:s(()=>[y(" 管理标签 ")]),_:1})])])]),e("div",_t,[vt,e("div",pt,[n(w,{color:c.value.is_enabled?"green":"red"},{default:s(()=>[y(a(c.value.is_enabled?"已启用":"已禁用"),1)]),_:1},8,["color"])])]),e("div",ft,[mt,e("div",ht,a(c.value.current_version||"未发布"),1)]),e("div",yt,[gt,e("div",bt,a(f(Q)(c.value.created_at)),1)]),e("div",kt,[wt,e("div",Ct,a(f(Q)(c.value.updated_at)),1)]),e("div",$t,[xt,e("div",Ot,a(c.value.created_by||"-"),1)]),e("div",St,[Tt,e("div",Mt,a(c.value.updated_by||"-"),1)]),c.value.type==="http"?(o(),r($,{key:0},[e("div",Pt,[Vt,e("div",Dt,a(((t=c.value.setting)==null?void 0:t.url)||"-"),1)]),e("div",zt,[Bt,e("div",Et,a(((k=c.value.setting)==null?void 0:k.method)||"-"),1)]),(R=(N=c.value.setting)==null?void 0:N.headers)!=null&&R.length?(o(),r("div",It,[Nt,e("div",Rt,[n($e,{columns:ve,"data-source":c.value.setting.headers,pagination:!1,size:"small",bordered:""},{bodyCell:s(({column:B,record:Ve})=>[B.key==="value"?(o(),r("span",Lt,a(Ve.value),1)):_("",!0)]),_:1},8,["data-source"])])])):_("",!0)],64)):_("",!0),c.value.type==="database"?(o(),r($,{key:1},[e("div",Ut,[qt,e("div",At,a(((oe=c.value.setting)==null?void 0:oe.url)||"-"),1)]),e("div",Ft,[jt,e("div",Ht,[e("pre",Kt,a(((ae=c.value.setting)==null?void 0:ae.sql)||"-"),1)])])],64)):_("",!0)])]}),_:1}),n(P,{key:"parameters",tab:"参数定义"},{default:s(()=>[!S.value||!S.value.properties||Object.keys(S.value.properties).length===0?(o(),p(I,{key:0,description:"无参数定义"})):(o(),r("div",Jt,[(o(!0),r($,null,V(S.value.properties,(t,k)=>(o(),r("div",{key:k,class:"parameter-item"},[e("div",Qt,[e("span",Gt,a(t.title||k),1),n(w,{class:"parameter-type"},{default:s(()=>[y(a(t.type||"string"),1)]),_:2},1024),t.location?(o(),p(w,{key:0,color:"purple"},{default:s(()=>[y(a(t.location),1)]),_:2},1024)):_("",!0),pe(k)?(o(),p(w,{key:1,color:"red"},{default:s(()=>[y("必填")]),_:1})):_("",!0)]),t.description?(o(),r("div",Wt,a(t.description),1)):_("",!0),t.enum||t.default!==void 0||t.minimum!==void 0||t.maximum!==void 0||t.format||t.items&&t.items.type?(o(),r("div",Xt,[t.enum?(o(),r("div",Yt,[Zt,e("div",es,[(o(!0),r($,null,V(t.enum,(N,R)=>(o(),p(w,{key:R,color:"blue"},{default:s(()=>[y(a(N),1)]),_:2},1024))),128))])])):_("",!0),t.format?(o(),r("div",ts,[ss,e("span",os,a(t.format),1)])):_("",!0),t.items&&t.items.type?(o(),r("div",as,[ls,e("span",ns,a(t.items.type),1)])):_("",!0),t.default!==void 0?(o(),r("div",is,[cs,e("span",rs,a(JSON.stringify(t.default)),1)])):_("",!0),t.minimum!==void 0?(o(),r("div",ds,[us,e("span",_s,a(t.minimum),1)])):_("",!0),t.maximum!==void 0?(o(),r("div",vs,[ps,e("span",fs,a(t.maximum),1)])):_("",!0),t.minLength!==void 0?(o(),r("div",ms,[hs,e("span",ys,a(t.minLength),1)])):_("",!0),t.maxLength!==void 0?(o(),r("div",gs,[bs,e("span",ks,a(t.maxLength),1)])):_("",!0)])):_("",!0)]))),128))]))]),_:1}),n(P,{key:"code",tab:"工具代码"},{default:s(()=>[e("div",ws,[e("pre",Cs,a(c.value.code),1)])]),_:1}),n(P,{key:"history",tab:"发布历史"},{default:s(()=>[L.value.length?(o(),r("div",$s,[(o(!0),r($,null,V(L.value,t=>(o(),r("div",{key:t.id,class:"version-item"},[e("div",xs,[Os,n(w,{class:"version-tag",color:c.value.current_version===t.version?"blue":"default",onClick:k=>he(t)},{default:s(()=>[y(" v"+a(t.version),1)]),_:2},1032,["color","onClick"]),Ss,e("span",Ts,a(f(Q)(t.created_at)),1),n(T,{type:"primary",size:"small",disabled:c.value.current_version===t.version,class:"rollback-button",onClick:k=>ye(t)},{icon:s(()=>[n(f(ie))]),default:s(()=>[y(" 回滚 ")]),_:2},1032,["disabled","onClick"])]),t.description?(o(),r("div",Ms,a(t.description),1)):_("",!0)]))),128))])):(o(),p(I,{key:0,description:"无发布历史"})),n(J,{open:E.value,"onUpdate:open":l[6]||(l[6]=t=>E.value=t),title:`工具代码 (v${O.value})`,width:"800px",footer:null,"ok-text":"确定","cancel-text":"取消"},{default:s(()=>[e("pre",Ps,a(M.value),1)]),_:1},8,["open","title"]),n(J,{open:D.value,"onUpdate:open":l[7]||(l[7]=t=>D.value=t),title:`确认回滚到版本 v${O.value}?`,width:"800px","confirm-loading":A.value,"ok-text":"确认回滚","cancel-text":"取消",onOk:ge},{default:s(()=>[e("div",Vs,[n(xe,{message:"警告",description:"回滚操作将使当前工具代码替换为选中版本的代码，请确认该操作。",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}})]),e("div",Ds,[e("div",zs,"版本 v"+a(O.value)+" 的代码:",1),e("pre",Bs,a(M.value),1)])]),_:1},8,["open","title","confirm-loading"])]),_:1}),n(P,{key:"configs",tab:"绑定配置"},{default:s(()=>[U.value.length?(o(),p(se,{key:1},{default:s(()=>[(o(!0),r($,null,V(U.value,t=>(o(),p(te,{key:t.id},{default:s(()=>[n(ee,null,{avatar:s(()=>[n(f(Ae),{class:"config-icon"})]),title:s(()=>[e("a",{class:"config-link",onClick:k=>f(m).push(`/config/${t.id}`)},a(t.name),9,Es)]),description:s(()=>[e("div",Is,a(t.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(o(),p(I,{key:0,description:"无绑定配置"}))]),_:1}),n(P,{key:"funcs",tab:"依赖函数"},{default:s(()=>[q.value.length?(o(),p(se,{key:1},{default:s(()=>[(o(!0),r($,null,V(q.value,t=>(o(),p(te,{key:t.id},{default:s(()=>[n(ee,null,{avatar:s(()=>[n(f(Fe),{class:"func-icon"})]),title:s(()=>[e("a",{class:"func-link",onClick:k=>f(m).push(`/func/${t.id}`)},a(t.name),9,Ns)]),description:s(()=>[e("div",Rs,a(t.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(o(),p(I,{key:0,description:"无依赖函数"}))]),_:1})]),_:1},8,["activeKey"])]),_:1})):_("",!0),n(J,{open:j.value,"onUpdate:open":l[10]||(l[10]=t=>j.value=t),title:"发布工具","confirm-loading":K.value,"ok-text":"确定","cancel-text":"取消",onOk:be},{default:s(()=>[n(Pe,{layout:"vertical"},{default:s(()=>[n(Me,{label:"发布描述"},{default:s(()=>[n(Te,{value:H.value,"onUpdate:value":l[9]||(l[9]=t=>H.value=t),rows:4,placeholder:"请输入发布描述"},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open","confirm-loading"]),n(He,{visible:F.value,"onUpdate:visible":l[11]||(l[11]=t=>F.value=t),"tool-id":parseInt(v.value),onUpdated:z},null,8,["visible","tool-id"])]),_:1})}}},Ks=ze(Ls,[["__scopeId","data-v-17663161"]]);export{Ks as default};
