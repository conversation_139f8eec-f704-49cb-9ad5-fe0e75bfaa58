import{_ as ce,u as pe,y as fe,z as L,r as p,a as me,o as ve,b as r,c as _,d as C,w as t,e as a,f as b,g as i,t as _e,k as z,h as g,P as ge,l as k,x as ye,F as K,q as w}from"./index-01e50f26.js";import{c as u,A as he,v as be}from"./AppLayout-94293e8c.js";import{_ as I}from"./MonacoEditor-e8f3b13e.js";import{J as ke}from"./JsonSchemaEditor-ba6fdea0.js";import{R as we}from"./RollbackOutlined-f857b260.js";import{S as Se}from"./SaveOutlined-a80652db.js";import{C as Te}from"./CloudUploadOutlined-c91fd750.js";/* empty css                                                     */const qe={class:"header-row"},Ce={class:"editor-container"},Ue={class:"tab-description"},Ee={class:"schema-editor-container"},Oe={class:"tab-description"},Me={class:"editor-container"},Le={__name:"ToolEditView",setup(Be){const j=pe(),B=fe(),f=L(()=>B.params.id),y=L(()=>!!f.value),d=L(()=>B.query.type||"basic"),m=p(!1),N=p(!1),U=p(!1),E=p(!1),A=p("parameters"),e=me({name:"",description:"",type:d.value,setting:d.value==="http"?{url:"",method:"POST",headers:[{key:"Content-Type",value:"application/json"}]}:d.value==="database"?{url:"",username:"",password:"",sql:`SELECT * FROM table_name
<where>
    <if test="id != null">
        AND id = #{id}
    </if>
</where>`}:{},parametersStr:d.value==="http"?JSON.stringify({type:"object",properties:{name:{type:"string",description:"名称",location:"body"}},required:["name"]}):d.value==="database"?JSON.stringify({type:"object",properties:{id:{type:"integer",description:"ID"}},required:[]}):JSON.stringify({type:"object",properties:{name:{type:"string",description:"名称"}},required:["name"]}),code:d.value==="http"?`# url: 请求地址
# method: 请求Method
# headers: 请求Header
# parameters: 传入工具参数
# config: 传入绑定配置
# result: 用于返回值

# 示例代码：
print("执行工具...")
result = easy_http_call(method, url, headers, parameters, config)`:d.value==="database"?`# url: 数据库连接URL
# username: 数据库用户名
# password: 数据库密码
# sql: 查询SQL
# parameters: 传入工具参数
# config: 传入绑定配置
# result: 用于返回值

# 示例代码：
print("执行工具...")
result = easy_database_call(url, username, password, sql, parameters, config)`:`# parameters: 传入工具参数
# config: 传入绑定配置
# result: 用于返回值

# 示例代码：
print("执行工具...")
result = {"message": "Hello, World!", "parameters": parameters, "config": config}`,func_ids:[],config_ids:[]}),P=p([]),x=p([]),G=[{title:"Key",dataIndex:"key",key:"key",width:"40%"},{title:"Value",dataIndex:"value",key:"value",width:"40%"},{title:"操作",key:"action",width:"20%"}],Q=()=>{e.setting.headers.push({key:"",value:""})},W=o=>{e.setting.headers.splice(o,1)},D=(o,s,c)=>{e.setting.headers[o]&&(e.setting.headers[o][s]=c)};ve(async()=>{await Promise.all([X(),Y()]),y.value&&await Z()});const X=async()=>{U.value=!0;try{await u({method:"get",url:"/api/v1/func",params:{page:1,size:100},onSuccess:o=>{P.value=o.map(s=>({value:s.id,label:s.name}))},errorMessage:"获取函数列表失败"})}finally{U.value=!1}},Y=async()=>{E.value=!0;try{await u({method:"get",url:"/api/v1/config",params:{page:1,size:100},onSuccess:o=>{x.value=o.map(s=>({value:s.id,label:s.name}))},errorMessage:"获取配置列表失败"})}finally{E.value=!1}},Z=async()=>{N.value=!0;try{await u({method:"get",url:`/api/v1/tool/${f.value}`,onSuccess:o=>{e.name=o.name,e.description=o.description||"",e.type=o.type||"basic",e.parametersStr=JSON.stringify(o.parameters,null,2),e.code=o.code,e.setting=o.setting||{url:"",headers:[]},e.setting.headers&&!Array.isArray(e.setting.headers)&&(e.setting.headers=[]),ee(),ae()},errorMessage:"获取工具数据失败"})}finally{N.value=!1}},ee=async()=>{try{await u({method:"get",url:`/api/v1/tool/${f.value}/func`,onSuccess:o=>{o&&Array.isArray(o)?e.func_ids=o.map(s=>s.id):e.func_ids=[]},errorMessage:"获取工具依赖函数失败"})}catch(o){console.error("Error fetching tool dependent functions:",o)}},ae=async()=>{try{await u({method:"get",url:`/api/v1/tool/${f.value}/config`,onSuccess:o=>{o&&Array.isArray(o)?e.config_ids=o.map(s=>s.id):e.config_ids=[]},errorMessage:"获取工具绑定配置失败"})}catch(o){console.error("Error fetching tool bound configs:",o)}},H=()=>{if(!e.name)return w.error("请输入工具名称"),!1;if(!e.description)return w.error("请输入工具描述"),!1;if(e.type==="http"&&!e.setting.url)return w.error("请输入HTTP请求地址"),!1;const o=be(e.parametersStr);return o.valid?e.code?!0:(w.error("请输入工具代码"),!1):(w.error("参数定义 JSON 格式不正确: "+o.message),!1)},J=()=>({name:e.name,description:e.description,type:e.type,setting:e.type==="http"?{url:e.setting.url,method:e.setting.method,headers:e.setting.headers.map(s=>({key:s.key,value:s.value}))}:e.type==="database"?{url:e.setting.url,username:e.setting.username,password:e.setting.password,sql:e.setting.sql}:{},parameters:JSON.parse(e.parametersStr),code:e.code,func_ids:e.func_ids,config_ids:e.config_ids}),te=async()=>{if(H()){m.value=!0;try{const o=J();y.value?await u({method:"put",url:`/api/v1/tool/${f.value}`,data:o,successMessage:"保存成功",errorMessage:"保存失败",onSuccess:()=>{h()}}):await u({method:"post",url:"/api/v1/tool",data:o,successMessage:"创建成功",errorMessage:"创建失败",onSuccess:()=>{h()}})}finally{m.value=!1}}},se=async()=>{if(H()){m.value=!0;try{const o=J();y.value?await u({method:"put",url:`/api/v1/tool/${f.value}/deploy`,data:o,successMessage:"保存并发布成功",errorMessage:"保存并发布失败",onSuccess:()=>{h()}}):await u({method:"post",url:"/api/v1/tool/deploy",data:o,successMessage:"创建并发布成功",errorMessage:"创建并发布失败",onSuccess:()=>{h()}})}finally{m.value=!1}}},h=()=>{j.back()};return(o,s)=>{const c=r("a-button"),oe=r("a-space"),v=r("a-input"),n=r("a-form-item"),le=r("a-textarea"),S=r("a-select-option"),O=r("a-select"),re=r("a-table"),T=r("a-col"),ne=r("a-input-password"),V=r("a-row"),R=r("a-alert"),F=r("a-tab-pane"),ie=r("a-tabs"),ue=r("a-form"),de=r("a-card");return _(),C(he,{"current-page-key":"tool"},{default:t(()=>[a(de,{title:y.value?"编辑工具":"创建工具"},{extra:t(()=>[a(oe,null,{default:t(()=>[a(c,{onClick:h},{icon:t(()=>[a(b(we))]),default:t(()=>[i(" 返回 ")]),_:1}),a(c,{type:"primary",loading:m.value,onClick:te},{icon:t(()=>[a(b(Se))]),default:t(()=>[i(" 保存 ")]),_:1},8,["loading"]),a(c,{type:"primary",loading:m.value,onClick:se},{icon:t(()=>[a(b(Te))]),default:t(()=>[i(" "+_e(y.value?"保存并发布":"创建并发布"),1)]),_:1},8,["loading"])]),_:1})]),default:t(()=>[a(ue,{model:e,layout:"vertical",class:"form-container"},{default:t(()=>[a(n,{label:"工具名称",required:""},{default:t(()=>[a(v,{value:e.name,"onUpdate:value":s[0]||(s[0]=l=>e.name=l),placeholder:"请输入工具名称"},null,8,["value"])]),_:1}),a(n,{label:"工具描述",required:""},{default:t(()=>[a(le,{value:e.description,"onUpdate:value":s[1]||(s[1]=l=>e.description=l),placeholder:"请输入工具描述",rows:3},null,8,["value"])]),_:1}),e.type==="http"?(_(),z(K,{key:0},[a(n,{label:"请求地址",required:""},{default:t(()=>[a(v,{value:e.setting.url,"onUpdate:value":s[2]||(s[2]=l=>e.setting.url=l),placeholder:"请输入HTTP请求地址，支持参数变量占位符，如：http://localhost:80/user/{id}?format={format}"},null,8,["value"])]),_:1}),a(n,{label:"请求方法",required:""},{default:t(()=>[a(O,{value:e.setting.method,"onUpdate:value":s[3]||(s[3]=l=>e.setting.method=l),placeholder:"请选择请求方法"},{default:t(()=>[a(S,{value:"GET"},{default:t(()=>[i("GET")]),_:1}),a(S,{value:"POST"},{default:t(()=>[i("POST")]),_:1}),a(S,{value:"PUT"},{default:t(()=>[i("PUT")]),_:1}),a(S,{value:"DELETE"},{default:t(()=>[i("DELETE")]),_:1})]),_:1},8,["value"])]),_:1}),a(n,{label:"请求Header","label-col":{span:24},"wrapper-col":{span:24},class:"header-form-item"},{default:t(()=>[g("div",qe,[a(c,{type:"primary",size:"small",onClick:Q},{icon:t(()=>[a(b(ge))]),default:t(()=>[i(" 添加请求头 ")]),_:1})]),a(re,{columns:G,"data-source":e.setting.headers,pagination:!1,size:"small"},{bodyCell:t(({column:l,record:$,index:M})=>[l.key==="key"?(_(),C(v,{key:0,"default-value":$.key,onBlur:q=>D(M,"key",q.target.value),placeholder:"请输入Header Key"},null,8,["default-value","onBlur"])):k("",!0),l.key==="value"?(_(),C(v,{key:1,"default-value":$.value,onBlur:q=>D(M,"value",q.target.value),placeholder:"请输入Header Value"},null,8,["default-value","onBlur"])):k("",!0),l.key==="action"?(_(),C(c,{key:2,type:"link",size:"small",danger:"",onClick:q=>W(M)},{icon:t(()=>[a(b(ye))]),default:t(()=>[i(" 删除 ")]),_:2},1032,["onClick"])):k("",!0)]),_:1},8,["data-source"])]),_:1})],64)):k("",!0),e.type==="database"?(_(),z(K,{key:1},[a(n,{label:"DB URL",required:""},{default:t(()=>[a(v,{value:e.setting.url,"onUpdate:value":s[4]||(s[4]=l=>e.setting.url=l),placeholder:"请输入数据库连接URL，例如：mysql://localhost:3306/easy_mcp"},null,8,["value"])]),_:1}),a(V,{gutter:16},{default:t(()=>[a(T,{span:12},{default:t(()=>[a(n,{label:"用户名",required:""},{default:t(()=>[a(v,{value:e.setting.username,"onUpdate:value":s[5]||(s[5]=l=>e.setting.username=l),placeholder:"请输入数据库用户名"},null,8,["value"])]),_:1})]),_:1}),a(T,{span:12},{default:t(()=>[a(n,{label:"密码",required:""},{default:t(()=>[a(ne,{value:e.setting.password,"onUpdate:value":s[6]||(s[6]=l=>e.setting.password=l),placeholder:"请输入数据库密码"},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(n,{label:"查询SQL",required:""},{default:t(()=>[g("div",Ce,[a(I,{value:e.setting.sql,"onUpdate:value":s[7]||(s[7]=l=>e.setting.sql=l),language:"xml",options:{automaticLayout:!0,scrollBeyondLastLine:!1,minimap:{enabled:!1}}},null,8,["value"])])]),_:1})],64)):k("",!0),a(V,{gutter:16},{default:t(()=>[a(T,{span:12},{default:t(()=>[a(n,{label:"依赖函数"},{default:t(()=>[a(O,{value:e.func_ids,"onUpdate:value":s[8]||(s[8]=l=>e.func_ids=l),mode:"multiple",placeholder:"请选择依赖函数",loading:U.value,options:P.value},null,8,["value","loading","options"])]),_:1})]),_:1}),a(T,{span:12},{default:t(()=>[a(n,{label:"绑定配置"},{default:t(()=>[a(O,{value:e.config_ids,"onUpdate:value":s[9]||(s[9]=l=>e.config_ids=l),mode:"multiple",placeholder:"请选择绑定配置",loading:E.value,options:x.value},null,8,["value","loading","options"])]),_:1})]),_:1})]),_:1}),a(n,null,{default:t(()=>[a(ie,{activeKey:A.value,"onUpdate:activeKey":s[12]||(s[12]=l=>A.value=l)},{default:t(()=>[a(F,{key:"parameters",tab:"参数定义","force-render":""},{default:t(()=>[g("div",Ue,[a(R,{message:"参数定义说明",description:"定义工具的输入参数。您可以使用图形化编辑器或直接编辑JSON Schema。",type:"info","show-icon":""})]),g("div",Ee,[a(ke,{value:e.parametersStr,"onUpdate:value":s[10]||(s[10]=l=>e.parametersStr=l),"is-http-tool":e.type==="http"},null,8,["value","is-http-tool"])])]),_:1}),a(F,{key:"code",tab:"工具代码","force-render":""},{default:t(()=>[g("div",Oe,[a(R,{message:"工具代码说明",description:"编写工具的执行代码。系统会自动提供以下变量：parameters（传入的参数）、config（绑定的配置）、result（用于返回结果）。您可以使用依赖函数和Python标准库。",type:"info","show-icon":""})]),g("div",Me,[a(I,{value:e.code,"onUpdate:value":s[11]||(s[11]=l=>e.code=l),language:"python",options:{automaticLayout:!0,scrollBeyondLastLine:!1}},null,8,["value"])])]),_:1})]),_:1},8,["activeKey"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])]),_:1})}}},Re=ce(Le,[["__scopeId","data-v-11b6a3be"]]);export{Re as default};
