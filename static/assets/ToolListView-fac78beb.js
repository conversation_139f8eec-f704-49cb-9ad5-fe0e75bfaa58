import{e as a,A as j,_ as ce,r as f,o as ue,b as m,c as u,d as O,w as l,k as b,F as U,g,l as R,q as he,u as be,h as F,f as d,P as Oe,D as ee,s as ke,t as D,v as we,E as te,x as Ce}from"./index-01e50f26.js";import{c as P,A as ze,T as ae,f as $e}from"./AppLayout-94293e8c.js";import{B as xe,T as Se}from"./ToolTagManager-e263c0c8.js";import{A as Te}from"./ApiOutlined-c053ae8c.js";var Pe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M811.4 418.7C765.6 297.9 648.9 212 512.2 212S258.8 297.8 213 418.6C127.3 441.1 64 519.1 64 612c0 110.5 89.5 200 199.9 200h496.2C870.5 812 960 722.5 960 612c0-92.7-63.1-170.7-148.6-193.3zm36.3 281a123.07 123.07 0 01-87.6 36.3H263.9c-33.1 0-64.2-12.9-87.6-36.3A123.3 123.3 0 01140 612c0-28 9.1-54.3 26.2-76.3a125.7 125.7 0 0166.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c54.3 14.5 92.1 63.8 92.1 120 0 33.1-12.9 64.3-36.3 87.7z"}}]},name:"cloud",theme:"outlined"};const Me=Pe;function ne(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},s=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),s.forEach(function(i){Ie(n,i,t[i])})}return n}function Ie(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var q=function(e,t){var s=ne({},e,t.attrs);return a(j,ne({},s,{icon:Me}),null)};q.displayName="CloudOutlined";q.inheritAttrs=!1;const De=q;var Ve={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"};const je=Ve;function le(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},s=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),s.forEach(function(i){Ae(n,i,t[i])})}return n}function Ae(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var J=function(e,t){var s=le({},e,t.attrs);return a(j,le({},s,{icon:je}),null)};J.displayName="DatabaseOutlined";J.inheritAttrs=!1;const oe=J;var He={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"};const Be=He;function se(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},s=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),s.forEach(function(i){Ee(n,i,t[i])})}return n}function Ee(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var Q=function(e,t){var s=se({},e,t.attrs);return a(j,se({},s,{icon:Be}),null)};Q.displayName="GlobalOutlined";Q.inheritAttrs=!1;const ie=Q;var Ne={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"};const Ge=Ne;function re(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},s=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),s.forEach(function(i){Le(n,i,t[i])})}return n}function Le(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var W=function(e,t){var s=re({},e,t.attrs);return a(j,re({},s,{icon:Ge}),null)};W.displayName="ImportOutlined";W.inheritAttrs=!1;const Fe=W;const Ue={key:0,class:"empty-state"},Re={__name:"ImportToolDialog",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","imported"],setup(n,{emit:e}){const t=n,s=v=>{v||(h.value=[])},i=[{title:"工具名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description"},{title:"配置",key:"hasConfig"}],y=f([]),k=f(!1),C=f(!1),h=f([]),z=async()=>{k.value=!0;try{await P({method:"get",url:"/api/v1/tool-builtin",onSuccess:v=>{y.value=(v==null?void 0:v.tools)||[]}})}catch(v){console.error("Failed to load builtin tools:",v)}finally{k.value=!1}},A=v=>{h.value=v},V=async()=>{if(h.value.length===0){he.warning("请选择要导入的工具");return}C.value=!0;try{await P({method:"post",url:"/api/v1/tool-builtin/import",data:{tool_id:h.value[0]},successMessage:"工具导入成功",onSuccess:()=>{e("imported"),$()}})}catch(v){console.error("Failed to import tool:",v)}finally{C.value=!1}},$=()=>{h.value=[],e("update:visible",!1)};return ue(()=>{s(t.visible),z()}),(v,X)=>{const H=m("a-empty"),_=m("a-tag"),B=m("a-table"),E=m("a-spin"),N=m("a-modal");return u(),O(N,{open:n.visible,title:"导入内置工具","confirm-loading":C.value,width:700,"ok-text":"确定","cancel-text":"取消",onOk:V,onCancel:$},{default:l(()=>[a(E,{spinning:k.value},{default:l(()=>[y.value.length===0&&!k.value?(u(),b("div",Ue,[a(H,{description:"没有可用的内置工具"})])):(u(),O(B,{key:1,columns:i,"data-source":y.value,pagination:!1,"row-selection":{selectedRowKeys:h.value,onChange:A,type:"radio"},"row-key":M=>M.id},{bodyCell:l(({column:M,record:G})=>[M.key==="hasConfig"?(u(),b(U,{key:0},[G.has_config?(u(),O(_,{key:0,color:"blue"},{default:l(()=>[g("有配置")]),_:1})):(u(),O(_,{key:1,color:"green"},{default:l(()=>[g("无配置")]),_:1}))],64)):R("",!0)]),_:1},8,["data-source","row-selection","row-key"]))]),_:1},8,["spinning"])]),_:1},8,["open","confirm-loading"])}}},qe=ce(Re,[["__scopeId","data-v-2bc8fd8b"]]);const Je={class:"action-bar"},Qe={class:"search-filters"},We=["onClick"],Xe={key:1},Ye={key:2,style:{display:"flex","flex-wrap":"wrap",gap:"4px","align-items":"center"}},Ze={key:0,style:{color:"#999"}},Ke={key:1},et={key:5},tt={__name:"ToolListView",setup(n){const e=be(),t=f(!1),s=f([]),i=f(0),y=f(1),k=f(10),C=f(""),h=f(!1),z=f([]),A=f([]),V=f([]),$=f(!1),v=f(null),X=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"标签",key:"tags",width:200},{title:"状态",key:"status",width:100},{title:"版本",key:"version",width:100,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:200,fixed:"right"}];ue(()=>{H(),_()});const H=async()=>{try{await P({method:"get",url:"/api/v1/tag",params:{page:1,size:1e3},onSuccess:c=>{A.value=c,V.value=c.map(o=>({label:o.name,value:o.id}))},errorMessage:"获取标签列表失败"})}catch(c){console.error("Error fetching tags:",c)}},_=async()=>{t.value=!0;try{await P({method:"get",url:"/api/v1/tool",params:{page:y.value,size:k.value,search:C.value||void 0,...z.value.length>0&&{tag_ids:z.value.join(",")}},onSuccess:(c,o)=>{s.value=c,i.value=o.total},errorMessage:"获取工具列表失败"})}finally{t.value=!1}},B=()=>{y.value=1,_()},E=()=>{y.value=1,_()},N=(c,o)=>{y.value=c,k.value=o,_()},M=(c,o)=>{y.value=1,k.value=o,_()},G=async c=>{try{await P({method:"delete",url:`/api/v1/tool/${c}`,successMessage:"删除成功",errorMessage:"删除失败"}),_()}catch(o){console.error("Error deleting tool:",o)}},de=async(c,o)=>{const x=s.value.find(S=>S.id===c);x&&(x.toggling=!0);try{await P({method:"patch",url:`/api/v1/tool/${c}/${o?"enable":"disable"}`,successMessage:`${o?"启用":"禁用"}成功`,errorMessage:`${o?"启用":"禁用"}失败`}),_()}catch(S){console.error(`Error ${o?"enabling":"disabling"} tool:`,S)}finally{x&&(x.toggling=!1)}},L=c=>{e.push(`/tool/create?type=${c}`)},pe=c=>{switch(c){case"http":return ie;case"database":return oe;default:return ae}},ge=c=>{v.value=c.id,$.value=!0};return(c,o)=>{const x=m("a-input-search"),S=m("a-select"),T=m("a-button"),I=m("a-menu-item"),Y=m("a-menu"),Z=m("a-dropdown"),K=m("a-tag"),me=m("a-switch"),fe=m("a-popconfirm"),ve=m("a-space"),_e=m("a-table"),ye=m("a-card");return u(),O(ze,{"current-page-key":"tool"},{default:l(()=>[a(ye,{title:"工具管理"},{default:l(()=>[F("div",Je,[F("div",Qe,[a(x,{value:C.value,"onUpdate:value":o[0]||(o[0]=r=>C.value=r),placeholder:"搜索工具名称或描述",style:{width:"300px"},onSearch:B},null,8,["value"]),a(S,{value:z.value,"onUpdate:value":o[1]||(o[1]=r=>z.value=r),mode:"multiple",placeholder:"按标签筛选",style:{width:"200px","margin-left":"12px"},options:V.value,onChange:E,"allow-clear":""},null,8,["value","options"])]),F("div",null,[a(Z,null,{overlay:l(()=>[a(Y,null,{default:l(()=>[a(I,{key:"basic",onClick:o[2]||(o[2]=r=>L("basic"))},{icon:l(()=>[a(d(ae))]),default:l(()=>[g(" 基础工具 ")]),_:1}),a(I,{key:"http",onClick:o[3]||(o[3]=r=>L("http"))},{icon:l(()=>[a(d(ie))]),default:l(()=>[g(" HTTP工具 ")]),_:1}),a(I,{key:"database",onClick:o[4]||(o[4]=r=>L("database"))},{icon:l(()=>[a(d(oe))]),default:l(()=>[g(" 数据库工具 ")]),_:1})]),_:1})]),default:l(()=>[a(T,{type:"primary",style:{"margin-right":"8px"}},{icon:l(()=>[a(d(Oe))]),default:l(()=>[g(" 创建工具 "),a(d(ee))]),_:1})]),_:1}),a(Z,null,{overlay:l(()=>[a(Y,null,{default:l(()=>[a(I,{key:"1",onClick:o[5]||(o[5]=r=>h.value=!0)},{default:l(()=>[a(d(Te)),g(" 导入内置工具 ")]),_:1}),a(I,{key:"2",onClick:o[6]||(o[6]=r=>d(e).push("/tool/import-openapi"))},{default:l(()=>[a(d(De)),g(" 导入OpenAPI ")]),_:1})]),_:1})]),default:l(()=>[a(T,null,{icon:l(()=>[a(d(Fe))]),default:l(()=>[g(" 导入工具 "),a(d(ee))]),_:1})]),_:1})])]),a(_e,{columns:X,"data-source":s.value,loading:t.value,pagination:{current:y.value,pageSize:k.value,total:i.value,onChange:N,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:M,showTotal:r=>`共 ${r} 条记录`},"row-key":r=>r.id},{bodyCell:l(({column:r,record:p})=>[r.key==="name"?(u(),b("a",{key:0,onClick:w=>d(e).push(`/tool/${p.id}`),style:{display:"flex","align-items":"center",gap:"6px"}},[(u(),O(ke(pe(p.type)),{style:{color:"#000"}})),g(" "+D(p.name),1)],8,We)):r.key==="description"?(u(),b("span",Xe,D(p.description||"无描述"),1)):r.key==="tags"?(u(),b("div",Ye,[(u(!0),b(U,null,we(p.tags,w=>(u(),O(K,{key:w.id,style:{margin:"0"}},{default:l(()=>[g(D(w.name),1)]),_:2},1024))),128)),!p.tags||p.tags.length===0?(u(),b("span",Ze," 无标签 ")):R("",!0),a(T,{type:"text",size:"small",onClick:w=>ge(p),style:{"margin-left":"4px",padding:"0",height:"auto","min-width":"auto"}},{icon:l(()=>[a(d(te))]),_:2},1032,["onClick"])])):r.key==="status"?(u(),O(me,{key:3,checked:p.is_enabled,loading:p.toggling,onChange:w=>de(p.id,w)},{checkedChildren:l(()=>[g("启用")]),unCheckedChildren:l(()=>[g("禁用")]),_:2},1032,["checked","loading","onChange"])):r.key==="version"?(u(),b(U,{key:4},[p.current_version?(u(),O(K,{key:0,color:"blue"},{default:l(()=>[g("v"+D(p.current_version),1)]),_:2},1024)):(u(),b("span",Ke,"未发布"))],64)):r.key==="created_at"?(u(),b("span",et,D(d($e)(p.created_at)),1)):r.key==="action"?(u(),O(ve,{key:6,size:0},{default:l(()=>[a(T,{type:"link",size:"small",onClick:w=>d(e).push(`/tool/${p.id}/debug`)},{icon:l(()=>[a(d(xe))]),default:l(()=>[g(" 调试 ")]),_:2},1032,["onClick"]),a(T,{type:"link",size:"small",onClick:w=>d(e).push(`/tool/${p.id}/edit`)},{icon:l(()=>[a(d(te))]),default:l(()=>[g(" 编辑 ")]),_:2},1032,["onClick"]),a(fe,{title:"确定要删除此工具吗？","ok-text":"确定","cancel-text":"取消",onConfirm:w=>G(p.id)},{default:l(()=>[a(T,{type:"link",size:"small",danger:""},{icon:l(()=>[a(d(Ce))]),default:l(()=>[g(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):R("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1}),a(qe,{visible:h.value,"onUpdate:visible":o[7]||(o[7]=r=>h.value=r),onImported:_},null,8,["visible"]),a(Se,{visible:$.value,"onUpdate:visible":o[8]||(o[8]=r=>$.value=r),"tool-id":v.value,onUpdated:_},null,8,["visible","tool-id"])]),_:1})}}},st=ce(tt,[["__scopeId","data-v-24bfe3b2"]]);export{st as default};
