import{_ as X,u as Z,r as b,a as A,o as ee,b as d,c as _,d as f,w as e,e as t,h as l,g as c,f as v,M as te,t as r,k as x,F as B,N as ae,l as C,U as F,p as se,m as le}from"./index-01e50f26.js";import{c as oe,A as ne,f as J}from"./AppLayout-94293e8c.js";import{C as ie}from"./ClearOutlined-b38cf02d.js";const g=z=>(se("data-v-2d975236"),z=z(),le(),z),ce={class:"action-bar"},ue={key:0},re={key:1},_e={key:0,class:"detail-content"},de={class:"info-item"},pe=g(()=>l("span",{class:"info-label"},"工具名称:",-1)),me={class:"info-item"},fe=g(()=>l("span",{class:"info-label"},"调用类型:",-1)),ye={class:"info-item"},ve=g(()=>l("span",{class:"info-label"},"调用状态:",-1)),ge={class:"info-item"},he=g(()=>l("span",{class:"info-label"},"耗时:",-1)),ke={class:"info-item"},we=g(()=>l("span",{class:"info-label"},"请求时间:",-1)),Ce={class:"info-item"},Se=g(()=>l("span",{class:"info-label"},"响应时间:",-1)),be={class:"info-item"},xe=g(()=>l("span",{class:"info-label"},"IP地址:",-1)),ze={class:"info-item"},Oe=g(()=>l("span",{class:"info-label"},"错误信息:",-1)),$e={class:"json-content"},Ne={class:"json-content"},Ie={__name:"ToolLogListView",setup(z){Z();const I=b(!1),T=b(!1),o=b(null),u=A({tool_name:"",call_type:void 0,is_success:void 0}),h=b([]),L=b([]),p=A({current:1,pageSize:20,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(a,s)=>`第 ${s[0]}-${s[1]} 条，共 ${a} 条`}),P=[{title:"工具名称",dataIndex:"tool_name",key:"tool_name",width:150},{title:"调用类型",dataIndex:"call_type",key:"call_type",width:100},{title:"调用状态",dataIndex:"is_success",key:"is_success",width:100},{title:"耗时",dataIndex:"duration_ms",key:"duration_ms",width:100},{title:"请求时间",dataIndex:"request_time",key:"request_time",width:180},{title:"操作",key:"actions",width:100}];ee(()=>{O()});const O=async()=>{I.value=!0;try{const a={page:p.current,size:p.pageSize};u.tool_name&&(a.tool_name=u.tool_name),u.call_type&&(a.call_type=u.call_type),u.is_success!==void 0&&(a.is_success=u.is_success),h.value&&h.value.length===2&&(a.start_time=h.value[0].valueOf(),a.end_time=h.value[1].valueOf()),await oe({method:"get",url:"/api/v1/tool-log",params:a,onSuccess:(s,i)=>{L.value=s,p.total=i.total},errorMessage:"获取工具调用日志失败"})}finally{I.value=!1}},M=a=>{p.current=a.current,p.pageSize=a.pageSize,O()},q=()=>{p.current=1,O()},R=()=>{q()},Y=()=>{u.tool_name="",u.call_type=void 0,u.is_success=void 0,h.value=[],p.current=1,O()},E=a=>{o.value=a,T.value=!0},V=a=>a?a<1e3?`${Math.round(a)}ms`:`${(a/1e3).toFixed(1)}s`:"0ms",$=a=>{if(!a)return"-";if(typeof a=="string"){const s=a.trim();if(s.startsWith("{")||s.startsWith("["))try{const i=JSON.parse(s);return JSON.stringify(i,null,2)}catch(i){return`// JSON解析错误: ${i.message}
${s}`}try{const i=JSON.parse(s);return JSON.stringify(i,null,2)}catch{return s}}if(typeof a=="object"&&a!==null)try{return JSON.stringify(a,null,2)}catch(s){return`// 对象序列化错误: ${s.message}
${String(a)}`}return String(a)},D=async a=>{try{await navigator.clipboard.writeText(a),console.log("复制成功")}catch{const i=document.createElement("textarea");i.value=a,document.body.appendChild(i),i.select();try{document.execCommand("copy"),console.log("复制成功")}catch(k){console.error("复制失败:",k)}document.body.removeChild(i)}};return(a,s)=>{const i=d("a-input"),k=d("a-select-option"),U=d("a-select"),H=d("a-range-picker"),j=d("a-space"),S=d("a-button"),w=d("a-tag"),W=d("a-table"),N=d("a-card"),y=d("a-col"),Q=d("a-typography-text"),G=d("a-row"),K=d("a-modal");return _(),f(ne,{"current-page-key":"tool-log"},{default:e(()=>[t(N,{title:"调用日志"},{default:e(()=>[l("div",ce,[t(j,null,{default:e(()=>[t(i,{value:u.tool_name,"onUpdate:value":s[0]||(s[0]=n=>u.tool_name=n),placeholder:"工具名称",style:{width:"150px"},"allow-clear":""},null,8,["value"]),t(U,{value:u.call_type,"onUpdate:value":s[1]||(s[1]=n=>u.call_type=n),placeholder:"调用类型",style:{width:"120px"},"allow-clear":""},{default:e(()=>[t(k,{value:"mcp"},{default:e(()=>[c("MCP调用")]),_:1}),t(k,{value:"debug"},{default:e(()=>[c("调试调用")]),_:1})]),_:1},8,["value"]),t(U,{value:u.is_success,"onUpdate:value":s[2]||(s[2]=n=>u.is_success=n),placeholder:"调用状态",style:{width:"120px"},"allow-clear":""},{default:e(()=>[t(k,{value:!0},{default:e(()=>[c("成功")]),_:1}),t(k,{value:!1},{default:e(()=>[c("失败")]),_:1})]),_:1},8,["value"]),t(H,{value:h.value,"onUpdate:value":s[3]||(s[3]=n=>h.value=n),"show-time":"",format:"YYYY-MM-DD HH:mm:ss",onChange:R,placeholder:["开始时间","结束时间"]},null,8,["value"])]),_:1}),t(j,null,{default:e(()=>[t(S,{type:"primary",onClick:q},{icon:e(()=>[t(v(te))]),default:e(()=>[c(" 查询 ")]),_:1}),t(S,{onClick:Y},{icon:e(()=>[t(v(ie))]),default:e(()=>[c(" 重置 ")]),_:1})]),_:1})]),t(W,{columns:P,"data-source":L.value,loading:I.value,pagination:{current:p.current,pageSize:p.pageSize,total:p.total,onChange:M,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:M,showTotal:n=>`共 ${n} 条记录`},"row-key":n=>n.id},{bodyCell:e(({column:n,record:m})=>[n.key==="tool_name"?(_(),f(w,{key:0,color:"blue"},{default:e(()=>[c(r(m.tool_name),1)]),_:2},1024)):n.key==="call_type"?(_(),f(w,{key:1,color:m.call_type==="mcp"?"purple":"orange"},{default:e(()=>[c(r(m.call_type==="mcp"?"MCP":"调试"),1)]),_:2},1032,["color"])):n.key==="is_success"?(_(),f(w,{key:2,color:m.is_success?"green":"red"},{default:e(()=>[c(r(m.is_success?"成功":"失败"),1)]),_:2},1032,["color"])):n.key==="duration_ms"?(_(),x(B,{key:3},[m.duration_ms?(_(),x("span",ue,r(V(m.duration_ms)),1)):(_(),x("span",re,"-"))],64)):n.key==="request_time"?(_(),x(B,{key:4},[c(r(v(J)(m.request_time)),1)],64)):n.key==="actions"?(_(),f(S,{key:5,type:"link",size:"small",onClick:Te=>E(m)},{icon:e(()=>[t(v(ae))]),default:e(()=>[c(" 详情 ")]),_:2},1032,["onClick"])):C("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1}),t(K,{open:T.value,"onUpdate:open":s[6]||(s[6]=n=>T.value=n),title:"调用详情",width:"900px",footer:null,class:"detail-modal"},{default:e(()=>[o.value?(_(),x("div",_e,[t(N,{title:"基本信息",size:"small",class:"info-card"},{default:e(()=>[t(G,{gutter:[16,16]},{default:e(()=>[t(y,{span:12},{default:e(()=>[l("div",de,[pe,t(w,{color:"blue"},{default:e(()=>[c(r(o.value.tool_name),1)]),_:1})])]),_:1}),t(y,{span:12},{default:e(()=>[l("div",me,[fe,t(w,{color:o.value.call_type==="mcp"?"purple":"orange"},{default:e(()=>[c(r(o.value.call_type==="mcp"?"MCP调用":"调试调用"),1)]),_:1},8,["color"])])]),_:1}),t(y,{span:12},{default:e(()=>[l("div",ye,[ve,t(w,{color:o.value.is_success?"green":"red"},{default:e(()=>[c(r(o.value.is_success?"成功":"失败"),1)]),_:1},8,["color"])])]),_:1}),t(y,{span:12},{default:e(()=>[l("div",ge,[he,l("span",null,r(o.value.duration_ms?V(o.value.duration_ms):"-"),1)])]),_:1}),t(y,{span:12},{default:e(()=>[l("div",ke,[we,l("span",null,r(v(J)(o.value.request_time)),1)])]),_:1}),t(y,{span:12},{default:e(()=>[l("div",Ce,[Se,l("span",null,r(o.value.response_time?v(J)(o.value.response_time):"-"),1)])]),_:1}),o.value.ip_address?(_(),f(y,{key:0,span:24},{default:e(()=>[l("div",be,[xe,l("span",null,r(o.value.ip_address),1)])]),_:1})):C("",!0),!o.value.is_success&&o.value.error_message?(_(),f(y,{key:1,span:24},{default:e(()=>[l("div",ze,[Oe,t(Q,{type:"danger"},{default:e(()=>[c(r(o.value.error_message),1)]),_:1})])]),_:1})):C("",!0)]),_:1})]),_:1}),o.value.request_params?(_(),f(N,{key:0,title:"请求参数",size:"small",class:"data-card"},{extra:e(()=>[t(S,{size:"small",type:"text",onClick:s[4]||(s[4]=n=>D($(o.value.request_params)))},{icon:e(()=>[t(v(F))]),default:e(()=>[c(" 复制 ")]),_:1})]),default:e(()=>[l("div",$e,[l("pre",null,r($(o.value.request_params)),1)])]),_:1})):C("",!0),o.value.response_data?(_(),f(N,{key:1,title:"响应数据",size:"small",class:"data-card"},{extra:e(()=>[t(S,{size:"small",type:"text",onClick:s[5]||(s[5]=n=>D($(o.value.response_data)))},{icon:e(()=>[t(v(F))]),default:e(()=>[c(" 复制 ")]),_:1})]),default:e(()=>[l("div",Ne,[l("pre",null,r($(o.value.response_data)),1)])]),_:1})):C("",!0)])):C("",!0)]),_:1},8,["open"])]),_:1})}}},qe=X(Ie,[["__scopeId","data-v-2d975236"]]);export{qe as default};
