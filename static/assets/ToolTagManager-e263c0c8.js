import{e as _,A as F,_ as U,r as d,z as x,B as q,b as v,c as u,d as m,w as p,h as i,k as g,F as B,v as I,g as S,t as k,l as M,f as G,P as J,p as Q,m as R}from"./index-01e50f26.js";import{c as h}from"./AppLayout-94293e8c.js";var W={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"};const X=W;function A(a){for(var o=1;o<arguments.length;o++){var t=arguments[o]!=null?Object(arguments[o]):{},r=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(n){Y(a,n,t[n])})}return a}function Y(a,o,t){return o in a?Object.defineProperty(a,o,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[o]=t,a}var b=function(o,t){var r=A({},o,t.attrs);return _(F,A({},r,{icon:X}),null)};b.displayName="BugOutlined";b.inheritAttrs=!1;const de=b;const P=a=>(Q("data-v-ea9ccff7"),a=a(),R(),a),Z={class:"tag-manager"},K={class:"current-tags"},ee=P(()=>i("h4",null,"当前标签",-1)),te={class:"tag-list"},ae={key:0,class:"empty-text"},se={class:"available-tags"},oe=P(()=>i("h4",null,"可用标签",-1)),ne={class:"tag-search"},le={class:"tag-list"},re={key:0,class:"empty-text"},ce={__name:"ToolTagManager",props:{visible:{type:Boolean,default:!1},toolId:{type:[Number,null],default:null}},emits:["update:visible","updated"],setup(a,{emit:o}){const t=a,r=d(!1),n=d(""),l=d([]),f=d([]),y=d([]),C=x(()=>{const e=l.value.map(s=>s.id);return f.value.filter(s=>!e.includes(s.id))}),T=x(()=>n.value?C.value.filter(e=>e.name.toLowerCase().includes(n.value.toLowerCase())||e.description&&e.description.toLowerCase().includes(n.value.toLowerCase())):C.value);q(()=>t.visible,e=>{e&&t.toolId&&N()});const N=async()=>{if(t.toolId)try{await h({method:"get",url:"/api/v1/tag",params:{page:1,size:1e3},onSuccess:e=>{f.value=e},errorMessage:"获取标签列表失败"}),await h({method:"get",url:`/api/v1/tool/${t.toolId}/tags`,onSuccess:e=>{l.value=e,y.value=e.map(s=>s.id)},errorMessage:"获取工具标签失败"})}catch(e){console.error("Error loading data:",e)}},V=e=>{l.value.find(s=>s.id===e.id)||l.value.push(e)},$=e=>{l.value=l.value.filter(s=>s.id!==e)},z=()=>{},L=async()=>{if(t.toolId)try{r.value=!0;const e=l.value.map(s=>s.id);await h({method:"put",url:`/api/v1/tool/${t.toolId}/tags`,data:{tag_ids:e},successMessage:"标签更新成功",errorMessage:"标签更新失败"}),o("updated"),O()}catch(e){console.error("Error updating tags:",e)}finally{r.value=!1}},O=()=>{o("update:visible",!1),n.value="",l.value=[],f.value=[],y.value=[]};return(e,s)=>{const w=v("a-tag"),j=v("a-divider"),E=v("a-input-search"),D=v("a-modal");return u(),m(D,{open:a.visible,title:"管理工具标签",onOk:L,onCancel:O,"confirm-loading":r.value,width:"600px","ok-text":"确定","cancel-text":"取消"},{default:p(()=>[i("div",Z,[i("div",K,[ee,i("div",te,[(u(!0),g(B,null,I(l.value,c=>(u(),m(w,{key:c.id,closable:"",onClose:H=>$(c.id)},{default:p(()=>[S(k(c.name),1)]),_:2},1032,["onClose"]))),128)),l.value.length===0?(u(),g("span",ae," 暂无标签 ")):M("",!0)])]),_(j),i("div",se,[oe,i("div",ne,[_(E,{value:n.value,"onUpdate:value":s[0]||(s[0]=c=>n.value=c),placeholder:"搜索标签",style:{"margin-bottom":"12px"},onSearch:z},null,8,["value"])]),i("div",le,[(u(!0),g(B,null,I(T.value,c=>(u(),m(w,{key:c.id,style:{cursor:"pointer","margin-bottom":"8px"},onClick:H=>V(c)},{icon:p(()=>[_(G(J))]),default:p(()=>[S(" "+k(c.name),1)]),_:2},1032,["onClick"]))),128)),T.value.length===0?(u(),g("span",re," 没有可添加的标签 ")):M("",!0)])])])]),_:1},8,["open","confirm-loading"])}}},ve=U(ce,[["__scopeId","data-v-ea9ccff7"]]);export{de as B,ve as T};
