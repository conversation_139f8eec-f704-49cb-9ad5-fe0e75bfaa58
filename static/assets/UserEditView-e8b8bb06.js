import{u as $,y as D,z as b,r as k,a as E,o as N,b as u,c as f,d as v,w as s,e as a,f as q,g as x,q as i}from"./index-01e50f26.js";import{c as _,A as O}from"./AppLayout-94293e8c.js";import{R as h}from"./RollbackOutlined-f857b260.js";import{S as z}from"./SaveOutlined-a80652db.js";const G={__name:"UserEditView",setup(F){const S=$(),U=D(),d=b(()=>U.params.id),l=b(()=>!!d.value),m=k(!1),w=k(!1),e=E({username:"",email:"",password:"",confirmPassword:""});N(()=>{l.value&&M()});const M=async()=>{w.value=!0;try{await _({method:"get",url:`/api/v1/user/${d.value}`,onSuccess:r=>{e.username=r.username,e.email=r.email},errorMessage:"获取用户数据失败"})}finally{w.value=!1}},R=()=>!l.value&&!e.username?(i.error("请输入用户名"),!1):e.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)?!l.value&&!e.password?(i.error("请输入密码"),!1):e.password&&e.password!==e.confirmPassword?(i.error("两次输入的密码不一致"),!1):!0:(i.error("请输入有效的邮箱地址"),!1):(i.error("请输入邮箱"),!1),C=()=>{const r={email:e.email};return l.value?e.password&&(r.password=e.password):(r.username=e.username,r.password=e.password),r},P=async()=>{if(R()){m.value=!0;try{const r=C();l.value?await _({method:"put",url:`/api/v1/user/${d.value}`,data:r,successMessage:"保存成功",errorMessage:"保存失败",onSuccess:()=>{c()}}):await _({method:"post",url:"/api/v1/user",data:r,successMessage:"创建成功",errorMessage:"创建失败",onSuccess:()=>{c()}})}finally{m.value=!1}}},c=()=>{S.back()};return(r,o)=>{const g=u("a-button"),A=u("a-space"),p=u("a-input"),n=u("a-form-item"),y=u("a-input-password"),B=u("a-form"),V=u("a-card");return f(),v(O,{"current-page-key":"user"},{default:s(()=>[a(V,{title:l.value?"编辑用户":"创建用户"},{extra:s(()=>[a(A,null,{default:s(()=>[a(g,{onClick:c},{icon:s(()=>[a(q(h))]),default:s(()=>[x(" 返回 ")]),_:1}),a(g,{type:"primary",loading:m.value,onClick:P},{icon:s(()=>[a(q(z))]),default:s(()=>[x(" 保存 ")]),_:1},8,["loading"])]),_:1})]),default:s(()=>[a(B,{model:e,layout:"vertical",class:"form-container"},{default:s(()=>[l.value?(f(),v(n,{key:1,label:"用户名"},{default:s(()=>[a(p,{value:e.username,"onUpdate:value":o[1]||(o[1]=t=>e.username=t),disabled:""},null,8,["value"])]),_:1})):(f(),v(n,{key:0,label:"用户名",required:""},{default:s(()=>[a(p,{value:e.username,"onUpdate:value":o[0]||(o[0]=t=>e.username=t),placeholder:"请输入用户名"},null,8,["value"])]),_:1})),a(n,{label:"邮箱",required:""},{default:s(()=>[a(p,{value:e.email,"onUpdate:value":o[2]||(o[2]=t=>e.email=t),placeholder:"请输入邮箱"},null,8,["value"])]),_:1}),a(n,{label:l.value?"新密码（留空不修改）":"密码",required:!l.value},{default:s(()=>[a(y,{value:e.password,"onUpdate:value":o[3]||(o[3]=t=>e.password=t),placeholder:"请输入密码"},null,8,["value"])]),_:1},8,["label","required"]),a(n,{label:l.value?"确认新密码":"确认密码",required:!l.value},{default:s(()=>[a(y,{value:e.confirmPassword,"onUpdate:value":o[4]||(o[4]=t=>e.confirmPassword=t),placeholder:"请再次输入密码"},null,8,["value"])]),_:1},8,["label","required"])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1})}}};export{G as default};
