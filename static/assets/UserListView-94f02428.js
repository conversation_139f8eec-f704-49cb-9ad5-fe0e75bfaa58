import{u as T,r as l,o as A,b as r,c as p,d as C,w as t,e as s,h as D,f as o,P as U,g,k as w,t as x,E as I,x as L,l as R}from"./index-01e50f26.js";import{c as S,A as j,f as q}from"./AppLayout-94293e8c.js";const F={class:"action-bar"},G=["onClick"],H={key:1},W={__name:"UserListView",setup(J){const _=T(),m=l(!1),f=l([]),y=l(0),i=l(1),d=l(10),h=l(""),z=[{title:"用户名",dataIndex:"username",key:"username",width:160,ellipsis:!0,tooltip:!0},{title:"邮箱",dataIndex:"email",key:"email",width:160,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:200,fixed:"right"}];A(()=>{u()});const u=async()=>{m.value=!0;try{await S({method:"get",url:"/api/v1/user",params:{page:i.value,size:d.value,search:h.value||void 0},onSuccess:(n,e)=>{f.value=n,y.value=e.total},errorMessage:"获取用户列表失败"})}finally{m.value=!1}},$=()=>{i.value=1,u()},b=(n,e)=>{i.value=n,d.value=e,u()},P=(n,e)=>{i.value=1,d.value=e,u()},V=async n=>{try{await S({method:"delete",url:`/api/v1/user/${n}`,successMessage:"删除成功",errorMessage:"删除失败"}),u()}catch(e){console.error("Error deleting user:",e)}};return(n,e)=>{const B=r("a-input-search"),v=r("a-button"),E=r("a-popconfirm"),M=r("a-space"),N=r("a-table"),O=r("a-card");return p(),C(j,{"current-page-key":"user"},{default:t(()=>[s(O,{title:"用户管理"},{default:t(()=>[D("div",F,[s(B,{value:h.value,"onUpdate:value":e[0]||(e[0]=a=>h.value=a),placeholder:"搜索用户名或邮箱",style:{width:"300px"},onSearch:$},null,8,["value"]),s(v,{type:"primary",onClick:e[1]||(e[1]=a=>o(_).push("/user/create"))},{icon:t(()=>[s(o(U))]),default:t(()=>[g(" 创建用户 ")]),_:1})]),s(N,{columns:z,"data-source":f.value,loading:m.value,pagination:{current:i.value,pageSize:d.value,total:y.value,onChange:b,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:P,showTotal:a=>`共 ${a} 条记录`},"row-key":a=>a.id},{bodyCell:t(({column:a,record:c})=>[a.key==="username"?(p(),w("a",{key:0,onClick:k=>o(_).push(`/user/${c.id}`)},x(c.username),9,G)):a.key==="created_at"?(p(),w("span",H,x(o(q)(c.created_at)),1)):a.key==="action"?(p(),C(M,{key:2,size:0},{default:t(()=>[s(v,{type:"link",size:"small",onClick:k=>o(_).push(`/user/${c.id}/edit`)},{icon:t(()=>[s(o(I))]),default:t(()=>[g(" 编辑 ")]),_:2},1032,["onClick"]),s(E,{title:"确定要删除此用户吗？","ok-text":"确定","cancel-text":"取消",onConfirm:k=>V(c.id)},{default:t(()=>[s(v,{type:"link",size:"small",danger:""},{icon:t(()=>[s(o(L))]),default:t(()=>[g(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):R("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1})]),_:1})}}};export{W as default};
